# Java到Go迁移详细计划

## 项目概述

**源项目**：
- 路径：`D:\wosm\ruoyi-java`
- 框架：Spring Boot + MyBatis
- 数据库：SQL Server 2012
- 缓存：Redis

**已迁移的基础项目**：
- 路径：`D:\wosm\backend`
- 迁移分析结果：`D:\wosm\migration`

**目标技术栈**：
- 框架：Go + Gin + Zap + GORM
- 数据库：SQL Server 2012
- 缓存：Redis
- 构建工具：go build

## 严格执行指南

本迁移计划必须被严格执行。AI助手在执行迁移任务时应遵循以下规则：

1. **任务格式**：每个任务应按照以下格式进行处理
   - 任务名称：对应计划中的具体项目
   - 输入文件：需要参考的文件路径，尤其是`D:\wosm\migration`目录下的分析文件
   - 输出文件：需要创建或修改的文件路径
   - 完成标准：确保满足所有列出的标准

2. **执行流程**：
   - 首先分析输入文件，特别是迁移分析文件，了解原Java代码结构
   - 检查目标文件是否存在及其当前内容
   - 根据分析创建或修改文件
   - 验证实现是否符合Go语言规范
   - 更新本文件中对应任务的状态

3. **完成报告**：每完成一个任务，应提供简要的完成报告：
   - 完成的文件路径
   - 实现的主要功能
   - 遇到的问题及解决方法
   - 下一步建议

4. **开发顺序**：应按照以下顺序进行开发，确保依赖关系正确：
   - 项目初始化(阶段0)
   - 领域模型(阶段1.1和阶段2)
   - 基础设施实现(阶段3)
   - 数据访问层(阶段4)
   - 服务层接口和实现(阶段1.3和阶段5)
   - 控制器(阶段1.2和阶段9)
   - 安全和中间件(阶段6和阶段7)
   - 工具类(阶段8)
   - 主程序和启动(阶段10)
   - 测试和文档(阶段11)
   - 构建和部署(阶段12)

## 迁移进度跟踪

本文档将按照以下格式跟踪每个文件的迁移状态：
- ✅ 已完成
- 🔄 进行中
- ⏳ 待处理
- ❌ 存在问题

## 阶段0：项目初始化与基础设置

### 0.1 项目结构初始化
- ✅ 创建`D:\wosm\backend\go.mod`文件
- ✅ 创建`D:\wosm\backend\.gitignore`文件

### 0.2 基础依赖安装
- ✅ 安装Gin框架
- ✅ 安装GORM及SQL Server驱动
- ✅ 安装Zap日志库
- ✅ 安装Redis客户端
- ✅ 安装配置管理库(viper)
- ✅ 安装JWT库

## 阶段1：修复和完善已迁移的代码结构

### 1.1 领域模型修复
- ✅ `D:\wosm\backend\internal\domain\base_entity.go`
- ✅ `D:\wosm\backend\internal\domain\tree_entity.go`

### 1.2 系统控制器修复
- ✅ `D:\wosm\backend\internal\system\sys_user_controller.go`
- ✅ `D:\wosm\backend\internal\system\sys_role_controller.go`
- ✅ `D:\wosm\backend\internal\system\sys_menu_controller.go`
- ✅ `D:\wosm\backend\internal\system\sys_dept_controller.go`
- ✅ `D:\wosm\backend\internal\system\sys_dict_type_controller.go`
- ✅ `D:\wosm\backend\internal\system\sys_dict_data_controller.go`
- ✅ `D:\wosm\backend\internal\system\sys_config_controller.go`
- ✅ `D:\wosm\backend\internal\system\sys_post_controller.go`
- ✅ `D:\wosm\backend\internal\system\sys_notice_controller.go`
- ✅ `D:\wosm\backend\internal\system\sys_login_controller.go`
- ✅ `D:\wosm\backend\internal\system\sys_profile_controller.go`
- ✅ `D:\wosm\backend\internal\system\sys_register_controller.go`
- ✅ `D:\wosm\backend\internal\system\sys_index_controller.go`

### 1.3 服务接口修复
- ✅ `D:\wosm\backend\internal\service\i_sys_user_service.go`
- ✅ `D:\wosm\backend\internal\service\i_sys_role_service.go`
- ✅ `D:\wosm\backend\internal\service\i_sys_menu_service.go`
- ✅ `D:\wosm\backend\internal\service\i_sys_dept_service.go`
- ✅ `D:\wosm\backend\internal\service\i_sys_dict_type_service.go`
- ✅ `D:\wosm\backend\internal\service\i_sys_dict_data_service.go`
- ✅ `D:\wosm\backend\internal\service\i_sys_config_service.go`
- ✅ `D:\wosm\backend\internal\service\i_sys_post_service.go`
- ✅ `D:\wosm\backend\internal\service\i_sys_notice_service.go`
- ✅ `D:\wosm\backend\internal\service\i_sys_job_service.go`
- ✅ `D:\wosm\backend\internal\service\i_sys_job_log_service.go`
- ✅ `D:\wosm\backend\internal\service\i_sys_logininfor_service.go`
- ✅ `D:\wosm\backend\internal\service\i_sys_oper_log_service.go`
- ✅ `D:\wosm\backend\internal\service\i_sys_user_online_service.go`
- ✅ `D:\wosm\backend\internal\service\token_service.go`
- ✅ `D:\wosm\backend\internal\service\sys_login_service.go`
- ✅ `D:\wosm\backend\internal\service\sys_permission_service.go`
- ✅ `D:\wosm\backend\internal\service\sys_password_service.go`
- ✅ `D:\wosm\backend\internal\service\sys_register_service.go`
- ✅ `D:\wosm\backend\internal\service\permission_service.go`

## 阶段2：添加缺失的领域模型

参考`D:\wosm\migration\com_ruoyi_common_core_domain_entity_structure.txt`和`D:\wosm\migration\com_ruoyi_system_domain_structure.txt`

### 2.1 系统实体模型
- ✅ `D:\wosm\backend\internal\domain\sys_user.go`
- ✅ `D:\wosm\backend\internal\domain\sys_role.go`
- ✅ `D:\wosm\backend\internal\domain\sys_menu.go`
- ✅ `D:\wosm\backend\internal\domain\sys_dept.go`
- ✅ `D:\wosm\backend\internal\domain\sys_dict_type.go`
- ✅ `D:\wosm\backend\internal\domain\sys_dict_data.go`
- ✅ `D:\wosm\backend\internal\domain\sys_config.go`
- ✅ `D:\wosm\backend\internal\domain\sys_post.go`
- ✅ `D:\wosm\backend\internal\domain\sys_notice.go`
- ✅ `D:\wosm\backend\internal\domain\sys_job.go`
- ✅ `D:\wosm\backend\internal\domain\sys_job_log.go`
- ✅ `D:\wosm\backend\internal\domain\sys_logininfor.go`
- ✅ `D:\wosm\backend\internal\domain\sys_oper_log.go`
- ✅ `D:\wosm\backend\internal\domain\sys_user_online.go`
- ✅ `D:\wosm\backend\internal\domain\sys_user_post.go`
- ✅ `D:\wosm\backend\internal\domain\sys_user_role.go`
- ✅ `D:\wosm\backend\internal\domain\sys_role_menu.go`
- ✅ `D:\wosm\backend\internal\domain\sys_role_dept.go`

### 2.2 视图对象模型(VO/DTO)
参考`D:\wosm\migration\com_ruoyi_system_domain_vo_structure.txt`和`D:\wosm\migration\com_ruoyi_common_core_domain_model_structure.txt`

- ✅ `D:\wosm\backend\internal\model\login_user.go`
- ✅ `D:\wosm\backend\internal\model\login_body.go`
- ✅ `D:\wosm\backend\internal\model\register_body.go`
- ✅ `D:\wosm\backend\internal\model\ajax_result.go`
- ✅ `D:\wosm\backend\internal\model\table_data_info.go`
- ✅ `D:\wosm\backend\internal\model\claims.go`
- ✅ `D:\wosm\backend\internal\model\router.go`

## 阶段3：基础设施实现

### 3.1 配置管理
参考`D:\wosm\migration\com_ruoyi_framework_config_structure.txt`

- ✅ `D:\wosm\backend\configs\config.yaml`
- ✅ `D:\wosm\backend\internal\config\app_config.go`
- ✅ `D:\wosm\backend\internal\config\server_config.go`
- ✅ `D:\wosm\backend\internal\config\database_config.go`
- ✅ `D:\wosm\backend\internal\config\redis_config.go`
- ✅ `D:\wosm\backend\internal\config\jwt_config.go`

### 3.2 日志系统
参考`D:\wosm\migration\com_ruoyi_common_utils_structure.txt`中的日志相关部分

- ✅ `D:\wosm\backend\pkg\logger\logger.go`
- ✅ `D:\wosm\backend\pkg\logger\zap_logger.go`
- ✅ `D:\wosm\backend\pkg\logger\file_logger.go`

### 3.3 数据库连接
参考`D:\wosm\migration\com_ruoyi_framework_datasource_structure.txt`

- ✅ `D:\wosm\backend\internal\database\database.go`
- ✅ `D:\wosm\backend\internal\database\transaction.go`
- ✅ `D:\wosm\backend\pkg\logger\gorm_logger.go`

### 3.4 Redis缓存
参考`D:\wosm\migration\com_ruoyi_common_core_redis_structure.txt`

- ✅ `D:\wosm\backend\internal\redis\redis_service.go`
- ✅ `D:\wosm\backend\internal\redis\redis_service_impl.go`

## 阶段4：数据访问层实现

参考`D:\wosm\migration\com_ruoyi_system_mapper_structure.txt`

### 4.1 基础Repository接口
- ✅ `D:\wosm\backend\internal\repository\base_repository.go`
- ✅ `D:\wosm\backend\internal\repository\user_repository.go`
- ✅ `D:\wosm\backend\internal\repository\role_repository.go`
- ✅ `D:\wosm\backend\internal\repository\menu_repository.go`
- ✅ `D:\wosm\backend\internal\repository\dept_repository.go`
- ✅ `D:\wosm\backend\internal\repository\dict_type_repository.go`
- ✅ `D:\wosm\backend\internal\repository\dict_data_repository.go`
- ✅ `D:\wosm\backend\internal\repository\config_repository.go`
- ✅ `D:\wosm\backend\internal\repository\post_repository.go`
- ✅ `D:\wosm\backend\internal\repository\notice_repository.go`
- ✅ `D:\wosm\backend\internal\repository\job_repository.go`
- ✅ `D:\wosm\backend\internal\repository\job_log_repository.go`
- ✅ `D:\wosm\backend\internal\repository\logininfor_repository.go`
- ✅ `D:\wosm\backend\internal\repository\oper_log_repository.go`
- ✅ `D:\wosm\backend\internal\repository\user_online_repository.go`

## 阶段5：服务层实现

参考`D:\wosm\migration\com_ruoyi_system_service_impl_structure.txt`

### 5.1 基础服务实现
- ✅ `D:\wosm\backend\internal\service\impl\sys_config_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_dict_type_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_dict_data_service_impl.go`

### 5.2 认证相关服务
- ✅ `D:\wosm\backend\internal\service\impl\sys_login_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\token_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_permission_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_register_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_password_service_impl.go`

### 5.3 核心业务服务
- ✅ `D:\wosm\backend\internal\service\impl\sys_user_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_role_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_menu_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_dept_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_post_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_notice_service_impl.go`

### 5.4 任务和日志服务
- ✅ `D:\wosm\backend\internal\service\impl\sys_job_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_job_log_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_logininfor_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_oper_log_service_impl.go`
- ✅ `D:\wosm\backend\internal\service\impl\sys_user_online_service_impl.go`

## 阶段6：安全和认证实现

参考`D:\wosm\migration\com_ruoyi_framework_security_context_structure.txt`和相关文件

### 6.1 认证框架
- ✅ `D:\wosm\backend\internal\auth\jwt.go`
- ✅ `D:\wosm\backend\internal\auth\token.go`
- ✅ `D:\wosm\backend\internal\auth\context.go`

### 6.2 权限框架
- ✅ `D:\wosm\backend\internal\auth\permission.go`
- ✅ `D:\wosm\backend\internal\auth\role.go`

## 阶段7：中间件实现

参考`D:\wosm\migration\com_ruoyi_framework_interceptor_structure.txt`和`D:\wosm\migration\com_ruoyi_common_filter_structure.txt`

### 7.1 基础中间件
- ✅ `D:\wosm\backend\internal\middleware\logger.go`
- ✅ `D:\wosm\backend\internal\middleware\recovery.go`
- ✅ `D:\wosm\backend\internal\middleware\cors.go`

### 7.2 安全中间件
- ✅ `D:\wosm\backend\internal\middleware\jwt_auth.go`
- ✅ `D:\wosm\backend\internal\middleware\permission.go`
- ✅ `D:\wosm\backend\internal\middleware\xss.go`
- ✅ `D:\wosm\backend\internal\middleware\repeat_submit.go`

## 阶段8：工具类实现

参考`D:\wosm\migration\com_ruoyi_common_utils_structure.txt`

### 8.1 通用工具类
参考`D:\wosm\migration\com_ruoyi_common_utils_structure.txt`

- ✅ `D:\wosm\backend\pkg\utils\string_utils.go`
- ✅ `D:\wosm\backend\pkg\utils\file_utils.go`
- ✅ `D:\wosm\backend\pkg\utils\date_utils.go`
- ✅ `D:\wosm\backend\pkg\utils\convert_utils.go`
- ✅ `D:\wosm\backend\pkg\utils\encryption_utils.go`
- ✅ `D:\wosm\backend\internal\utils\uuid.go`
- ✅ `D:\wosm\backend\internal\utils\ip.go`

## 阶段9：路由和控制器完善

### 9.1 路由注册
- ✅ `D:\wosm\backend\internal\router/router.go`
- ✅ `D:\wosm\backend\internal\router/system_router.go`
- ✅ `D:\wosm\backend\internal\router/monitor_router.go`
- ✅ `D:\wosm\backend\internal\router/common_router.go`

### 9.2 控制器逻辑实现
- ⏳ 完成系统控制器中的所有TODO方法

## 阶段10：主程序和启动

### 10.1 主程序
- ⏳ `D:\wosm\backend\cmd\main.go`

### 10.2 应用初始化
- ⏳ `D:\wosm\backend\internal\initialize\config.go`
- ⏳ `D:\wosm\backend\internal\initialize\logger.go`
- ⏳ `D:\wosm\backend\internal\initialize\database.go`
- ⏳ `D:\wosm\backend\internal\initialize\redis.go`
- ⏳ `D:\wosm\backend\internal\initialize\router.go`

## 阶段11：测试和文档

### 11.1 单元测试
- ⏳ 为核心服务编写单元测试

### 11.2 API文档
- ⏳ `D:\wosm\backend\docs\api.md`

### 11.3 部署文档
- ⏳ `D:\wosm\backend\docs\deployment.md`

## 阶段12：构建和部署

### 12.1 构建脚本
- ⏳ `D:\wosm\backend\build.sh`
- ⏳ `D:\wosm\backend\build.bat`

### 12.2 Docker配置
- ⏳ `D:\wosm\backend\Dockerfile`
- ⏳ `D:\wosm\backend\docker-compose.yml`

## 迁移指南与参考

在迁移过程中，请参考以下文件：

1. `D:\wosm\migration\go_migration_guide.txt` - 包含Java到Go的类型映射和包映射指南
2. `D:\wosm\migration\package_structure.txt` - 包含整体包结构信息

对于每个Go文件的实现，请参考对应的Java结构文件，例如：
- 实现`sys_user_service_impl.go`时，参考`D:\wosm\migration\com_ruoyi_system_service_impl_structure.txt`中的用户服务相关部分
- 实现实体模型时，参考`D:\wosm\migration\com_ruoyi_common_core_domain_entity_structure.txt`和`D:\wosm\migration\com_ruoyi_system_domain_structure.txt`