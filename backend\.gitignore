# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Dependency directories
vendor/

# Go workspace file
go.work

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Log files
*.log
logs/

# Build output
bin/
build/
dist/

# Config files with sensitive information
*.local.yaml
*.local.yml

# OS specific files
.DS_Store
Thumbs.db

# Temporary files
tmp/
temp/

# Upload directory
uploads/ 