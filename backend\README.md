# RuoYi-Go

RuoYi-Go是一个基于Go语言的后台管理系统，是对Java版RuoYi系统的Go语言实现。

## 项目介绍

本项目是将若依(RuoYi)管理系统从Java Spring Boot技术栈完整迁移到Go Gin技术栈的实现。目标是**100%复刻Java后端功能**，确保前端Vue应用无需任何修改即可对接Go后端。

### 迁移目标
- ✅ **功能完全一致**：所有Java后端功能在Go中完全实现
- ✅ **接口完全兼容**：API路径、参数、响应格式与Java版本完全一致
- ✅ **数据结构一致**：数据库表结构、字段类型、约束完全一致
- ✅ **业务逻辑一致**：权限控制、数据验证、业务流程保持一致

## 技术栈

- **Web框架**: Gin
- **ORM框架**: GORM
- **数据库**: SQL Server
- **缓存**: Redis
- **认证**: JWT
- **日志**: Zap
- **配置管理**: Viper

## 快速开始

### 环境准备

#### 必需环境
- **Go 1.24+**
- **SQL Server 2012+**
- **Redis 6.0+**

### 本地开发

```bash
# 1. 克隆项目
git clone <repository-url>
cd ruoyi-go

# 2. 配置数据库
# 修改 configs/config.yaml 中的数据库连接信息

# 3. 下载依赖
go mod tidy

# 4. 运行应用
go run cmd/main.go
```

## 项目结构

```
backend/
├── cmd/                    # 命令行应用
│   └── main.go            # 主程序入口
├── configs/                # 配置文件
│   └── config.yaml        # 主配置文件
├── internal/               # 内部包
│   ├── api/               # API层
│   │   ├── controller/    # 控制器
│   │   ├── middleware/    # 中间件
│   │   └── router/        # 路由
│   ├── model/             # 数据模型
│   ├── service/           # 服务层
│   ├── repository/        # 数据访问层
│   ├── domain/            # 领域模型
│   ├── utils/             # 工具类
│   ├── config/            # 配置结构
│   ├── cache/             # 缓存
│   └── middleware/        # 中间件
├── pkg/                    # 公共包
│   ├── logger/            # 日志
│   └── utils/             # 工具类
├── logs/                   # 日志目录
├── uploads/                # 上传文件目录
├── go.mod                  # Go模块文件
├── go.sum                  # 依赖锁定文件
└── README.md               # 项目说明
```

## 许可证

[MIT](LICENSE) 