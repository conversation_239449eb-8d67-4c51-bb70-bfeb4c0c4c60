@echo off
setlocal

echo ===================================
echo RuoYi-Go 构建脚本
echo ===================================

:: 设置变量
set OUTPUT_DIR=build
set BINARY_NAME=ruoyi-go
set CONFIG_DIR=configs

:: 检查Go是否安装
where go >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到Go。请确保Go已安装并添加到PATH中。
    exit /b 1
)

:: 显示Go版本
echo 使用的Go版本:
go version

:: 清理旧的构建
echo 清理旧的构建...
if exist %OUTPUT_DIR% (
    rmdir /s /q %OUTPUT_DIR%
)
mkdir %OUTPUT_DIR%

:: 下载依赖
echo 下载依赖...
go mod tidy

:: 构建应用
echo 构建应用...
go build -o %OUTPUT_DIR%\%BINARY_NAME%.exe .\cmd\main.go

:: 检查构建结果
if %ERRORLEVEL% neq 0 (
    echo 构建失败!
    exit /b 1
)

:: 复制配置文件
echo 复制配置文件...
xcopy /s /y %CONFIG_DIR% %OUTPUT_DIR%\%CONFIG_DIR%\

:: 创建日志和上传目录
mkdir %OUTPUT_DIR%\logs
mkdir %OUTPUT_DIR%\uploads

:: 创建启动脚本
echo @echo off > %OUTPUT_DIR%\start.bat
echo echo 启动 RuoYi-Go 服务... >> %OUTPUT_DIR%\start.bat
echo start /b %BINARY_NAME%.exe >> %OUTPUT_DIR%\start.bat
echo echo 服务已启动! >> %OUTPUT_DIR%\start.bat

echo ===================================
echo 构建成功! 
echo 可执行文件位于: %OUTPUT_DIR%\%BINARY_NAME%.exe
echo 使用 %OUTPUT_DIR%\start.bat 启动服务
echo ===================================

endlocal 