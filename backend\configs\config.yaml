# 应用配置
app:
  # 名称
  name: "RuoYi-Go"
  # 版本
  version: "1.0.0"
  # 环境
  env: "dev"
  # 端口
  port: 8080
  # JWT密钥
  jwt_secret: "ruoyi-go-jwt-secret"
  basePath: "./"
  uploadPath: "./upload"
  resourcePath: "./resource"
  avatarPath: "./upload/avatar"
  downloadPath: "./download"
  logPath: "./logs"
  tempPath: "./temp"

# 服务器配置
server:
  # 地址
  host: "0.0.0.0"
  # 端口
  port: 8080
  # 上下文路径
  context_path: ""
  # 最大请求大小
  max_request_size: "50MB"
  # 会话超时（分钟）
  session_timeout: 30
  # 是否开启HTTPS
  use_ssl: false
  # SSL证书路径
  ssl_cert: ""
  # SSL密钥路径
  ssl_key: ""
  read_timeout: 60s
  write_timeout: 60s
  idle_timeout: 120s

# 数据库配置
database:
  # 类型
  type: "sqlserver"
  # 地址
  host: "localhost"
  # 端口
  port: 1433
  # 用户名
  username: "sa"
  # 密码
  password: "F@2233"
  # 数据库名
  database: "ruoyi"
  # 参数
  params: ""
  # 最大空闲连接数
  max_idle_conns: 10
  # 最大打开连接数
  max_open_conns: 100
  # 连接最大生命周期（小时）
  conn_max_lifetime: 1

# Redis配置
redis:
  # 地址
  host: "localhost"
  # 端口
  port: 6379
  # 密码
  password: ""
  # 数据库
  db: 0
  # 连接池大小
  pool_size: 100
  # 最小空闲连接数
  min_idle_conns: 10
  # 空闲超时（分钟）
  idle_timeout: 5
  # 连接超时（秒）
  dial_timeout: 3
  # 读取超时（秒）
  read_timeout: 3
  # 写入超时（秒）
  write_timeout: 3

# JWT配置
jwt:
  # 密钥
  secret: "abcdefghijklmnopqrstuvwxyz"
  # 过期时间（分钟）
  expire_time: 120
  # 刷新时间（分钟）
  refresh_time: 240
  # 签发者
  issuer: "ruoyi-go"
  # 主题
  subject: "user"
  # 受众
  audience: "app"
  # 签名算法
  signing_method: "HS256"

# 日志配置
log:
  # 级别
  level: "info"
  # 格式
  format: "json"
  # 输出
  output: "console"
  # 文件路径
  file_path: "logs/app.log"
  # 最大大小（MB）
  max_size: 100
  # 最大备份数
  max_backups: 10
  # 最大保存天数
  max_age: 30
  # 是否压缩
  compress: true
  # 是否输出调用者信息
  caller: true

# 上传配置
upload:
  # 上传路径
  path: "upload"
  # 允许的文件类型
  allowed_types: "jpg,jpeg,png,gif,doc,docx,xls,xlsx,ppt,pptx,pdf,txt,zip,rar"
  # 最大文件大小（MB）
  max_size: 50
  # 是否使用原始文件名
  use_original_name: false

# 验证码配置
captcha:
  # 是否开启验证码
  enabled: true
  # 验证码类型（math/char）
  type: "math"
  # 验证码长度
  length: 4
  # 验证码宽度
  width: 160
  # 验证码高度
  height: 60
  # 验证码过期时间（分钟）
  expire_time: 2
  # 验证码干扰线数量
  noise_count: 4
  # 验证码字体
  font_size: 40

# 线程池配置
thread_pool:
  # 核心线程数
  core_pool_size: 10
  # 最大线程数
  max_pool_size: 20
  # 队列容量
  queue_capacity: 100
  # 线程保持活动时间（秒）
  keep_alive_seconds: 60
  # 线程名称前缀
  thread_name_prefix: "async-"

# 安全配置
security:
  # 是否开启XSS过滤
  xss_enabled: true
  # XSS排除路径
  xss_excludes: "/system/notice/*"
  # 是否开启CSRF防护
  csrf_enabled: false
  # 是否允许同一账号多处登录
  allow_multi_login: false
  # 密码最大错误次数
  max_retry_count: 5
  # 密码锁定时间（分钟）
  lock_time: 10
  # 是否允许注册
  allow_register: true
  # 密码强度正则表达式
  password_pattern: "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$"

# 代码生成配置
gen:
  # 作者
  author: "ruoyi"
  # 默认生成包路径
  package_name: "com.ruoyi.system"
  # 自动去除表前缀
  auto_remove_pre: true
  # 表前缀
  table_prefix: "sys_" 