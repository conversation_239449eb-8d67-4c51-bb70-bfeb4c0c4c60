version: '3'

services:
  ruoyi-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ruoyi-backend
    restart: always
    ports:
      - "8080:8080"
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./configs:/app/configs
    depends_on:
      - redis
    networks:
      - ruoyi-net
    environment:
      - TZ=Asia/Shanghai

  redis:
    image: redis:6-alpine
    container_name: ruoyi-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    networks:
      - ruoyi-net

  # 如果需要，可以添加SQL Server服务
  # sqlserver:
  #   image: mcr.microsoft.com/mssql/server:2019-latest
  #   container_name: ruoyi-sqlserver
  #   restart: always
  #   ports:
  #     - "1433:1433"
  #   environment:
  #     - ACCEPT_EULA=Y
  #     - SA_PASSWORD=YourStrongPassword123
  #     - MSSQL_PID=Express
  #   volumes:
  #     - sqlserver-data:/var/opt/mssql
  #   networks:
  #     - ruoyi-net

networks:
  ruoyi-net:
    driver: bridge

volumes:
  redis-data:
  # sqlserver-data: 