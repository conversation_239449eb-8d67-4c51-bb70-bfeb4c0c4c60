package config

// AppConfig 应用配置
type AppConfig struct {
	// 名称
	Name string `mapstructure:"name" json:"name"`

	// 版本
	Version string `mapstructure:"version" json:"version"`

	// 环境
	Env string `mapstructure:"env" json:"env"`

	// 端口
	Port int `mapstructure:"port" json:"port"`

	// JWT密钥
	JWTSecret string `mapstructure:"jwt_secret" json:"jwt_secret"`

	// 服务器配置
	Server ServerConfig `mapstructure:"server" json:"server"`

	// 数据库配置
	Database DBConfig `mapstructure:"database" json:"database"`

	// Redis配置
	Redis RedisCfg `mapstructure:"redis" json:"redis"`

	// JWT配置
	JWT JWTConfig `mapstructure:"jwt" json:"jwt"`
}

// IsDev 是否是开发环境
func (c *AppConfig) IsDev() bool {
	return c.Env == "dev"
}

// IsTest 是否是测试环境
func (c *AppConfig) IsTest() bool {
	return c.Env == "test"
}

// IsProd 是否是生产环境
func (c *AppConfig) IsProd() bool {
	return c.Env == "prod"
}

// RedisConfig Redis配置
type RedisConfig struct {
	// 地址
	Host string `mapstructure:"host" json:"host"`

	// 端口
	Port int `mapstructure:"port" json:"port"`

	// 密码
	Password string `mapstructure:"password" json:"password"`

	// 数据库
	DB int `mapstructure:"db" json:"db"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	// 类型
	Type string `mapstructure:"type" json:"type"`

	// 地址
	Host string `mapstructure:"host" json:"host"`

	// 端口
	Port int `mapstructure:"port" json:"port"`

	// 用户名
	Username string `mapstructure:"username" json:"username"`

	// 密码
	Password string `mapstructure:"password" json:"password"`

	// 数据库名
	Database string `mapstructure:"database" json:"database"`

	// 参数
	Params string `mapstructure:"params" json:"params"`
}
