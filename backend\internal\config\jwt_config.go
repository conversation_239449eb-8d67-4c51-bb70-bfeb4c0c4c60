package config

import (
	"time"
)

// JWTConfig JWT配置
type JWTConfig struct {
	// 密钥
	Secret string `mapstructure:"secret" json:"secret"`

	// 过期时间（分钟）
	ExpireTime int `mapstructure:"expire_time" json:"expire_time"`

	// 刷新时间（分钟）
	RefreshTime int `mapstructure:"refresh_time" json:"refresh_time"`

	// 签发者
	Issuer string `mapstructure:"issuer" json:"issuer"`

	// 主题
	Subject string `mapstructure:"subject" json:"subject"`

	// 受众
	Audience string `mapstructure:"audience" json:"audience"`

	// 签名算法
	SigningMethod string `mapstructure:"signing_method" json:"signing_method"`
}

// GetExpireTime 获取过期时间
func (c *JWTConfig) GetExpireTime() time.Duration {
	return time.Duration(c.ExpireTime) * time.Minute
}

// GetRefreshTime 获取刷新时间
func (c *JWTConfig) GetRefreshTime() time.Duration {
	return time.Duration(c.RefreshTime) * time.Minute
}

// GetSigningMethod 获取签名算法
func (c *JWTConfig) GetSigningMethod() string {
	if c.SigningMethod == "" {
		return "HS256"
	}
	return c.SigningMethod
}
