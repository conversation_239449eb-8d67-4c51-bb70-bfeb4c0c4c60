package config

import (
	"fmt"
	"time"
)

// RedisCfg Redis配置
type RedisCfg struct {
	// 地址
	Host string `mapstructure:"host" json:"host"`

	// 端口
	Port int `mapstructure:"port" json:"port"`

	// 密码
	Password string `mapstructure:"password" json:"password"`

	// 数据库
	DB int `mapstructure:"db" json:"db"`

	// 连接池大小
	PoolSize int `mapstructure:"pool_size" json:"pool_size"`

	// 最小空闲连接数
	MinIdleConns int `mapstructure:"min_idle_conns" json:"min_idle_conns"`

	// 空闲超时（分钟）
	IdleTimeout int `mapstructure:"idle_timeout" json:"idle_timeout"`

	// 连接超时（秒）
	DialTimeout int `mapstructure:"dial_timeout" json:"dial_timeout"`

	// 读取超时（秒）
	ReadTimeout int `mapstructure:"read_timeout" json:"read_timeout"`

	// 写入超时（秒）
	WriteTimeout int `mapstructure:"write_timeout" json:"write_timeout"`
}

// GetAddr 获取Redis地址
func (c *RedisCfg) GetAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// GetIdleTimeout 获取空闲超时时间
func (c *RedisCfg) GetIdleTimeout() time.Duration {
	return time.Duration(c.IdleTimeout) * time.Minute
}

// GetDialTimeout 获取连接超时时间
func (c *RedisCfg) GetDialTimeout() time.Duration {
	return time.Duration(c.DialTimeout) * time.Second
}

// GetReadTimeout 获取读取超时时间
func (c *RedisCfg) GetReadTimeout() time.Duration {
	return time.Duration(c.ReadTimeout) * time.Second
}

// GetWriteTimeout 获取写入超时时间
func (c *RedisCfg) GetWriteTimeout() time.Duration {
	return time.Duration(c.WriteTimeout) * time.Second
}
