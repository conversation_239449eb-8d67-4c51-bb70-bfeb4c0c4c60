package config

import (
	"time"
)

// ServerConfig 服务器配置
type ServerConfig struct {
	// 主机地址
	Host string `mapstructure:"host" json:"host"`

	// 端口
	Port int `mapstructure:"port" json:"port"`

	// 上下文路径
	ContextPath string `mapstructure:"context_path" json:"context_path"`

	// 最大请求大小
	MaxRequestSize string `mapstructure:"max_request_size" json:"max_request_size"`

	// 会话超时（分钟）
	SessionTimeout int `mapstructure:"session_timeout" json:"session_timeout"`

	// 是否开启HTTPS
	UseSSL bool `mapstructure:"use_ssl" json:"use_ssl"`

	// SSL证书路径
	SSLCert string `mapstructure:"ssl_cert" json:"ssl_cert"`

	// SSL密钥路径
	SSLKey string `mapstructure:"ssl_key" json:"ssl_key"`

	// 读取超时
	ReadTimeout time.Duration `mapstructure:"read_timeout" json:"read_timeout"`

	// 写入超时
	WriteTimeout time.Duration `mapstructure:"write_timeout" json:"write_timeout"`

	// 空闲超时
	IdleTimeout time.Duration `mapstructure:"idle_timeout" json:"idle_timeout"`
}

// GetAddress 获取服务器地址
func (s *ServerConfig) GetAddress() string {
	return s.Host + ":" + string(s.Port)
}

// GetContextPath 获取上下文路径
func (s *ServerConfig) GetContextPath() string {
	return s.ContextPath
}

// GetSessionTimeoutDuration 获取会话超时时间
func (s *ServerConfig) GetSessionTimeoutDuration() time.Duration {
	return time.Duration(s.SessionTimeout) * time.Minute
}

// IsSSLEnabled 是否启用SSL
func (s *ServerConfig) IsSSLEnabled() bool {
	return s.UseSSL && s.SSLCert != "" && s.SSLKey != ""
}

// GetReadTimeout 获取读取超时时间
func (s *ServerConfig) GetReadTimeout() time.Duration {
	if s.ReadTimeout <= 0 {
		return 60 * time.Second // 默认60秒
	}
	return s.ReadTimeout
}

// GetWriteTimeout 获取写入超时时间
func (s *ServerConfig) GetWriteTimeout() time.Duration {
	if s.WriteTimeout <= 0 {
		return 60 * time.Second // 默认60秒
	}
	return s.WriteTimeout
}

// GetIdleTimeout 获取空闲超时时间
func (s *ServerConfig) GetIdleTimeout() time.Duration {
	if s.IdleTimeout <= 0 {
		return 120 * time.Second // 默认120秒
	}
	return s.IdleTimeout
}
