package database

import (
	"fmt"
	"sync"

	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/pkg/logger"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

var (
	// DB 全局数据库连接
	DB *gorm.DB
	// 确保只初始化一次
	once sync.Once
)

// Options 数据库选项
type Options struct {
	// 配置
	Config *config.DBConfig
	// 是否启用日志
	EnableLog bool
	// 是否启用自动迁移
	EnableAutoMigrate bool
}

// Option 选项函数
type Option func(*Options)

// WithConfig 设置数据库配置
func WithConfig(cfg *config.DBConfig) Option {
	return func(o *Options) {
		o.Config = cfg
	}
}

// WithLog 设置是否启用日志
func WithLog(enable bool) Option {
	return func(o *Options) {
		o.EnableLog = enable
	}
}

// WithAutoMigrate 设置是否启用自动迁移
func WithAutoMigrate(enable bool) Option {
	return func(o *Options) {
		o.EnableAutoMigrate = enable
	}
}

// Setup 设置数据库连接
func Setup(opts ...Option) error {
	var err error
	once.Do(func() {
		err = setup(opts...)
	})
	return err
}

// setup 设置数据库连接
func setup(opts ...Option) error {
	// 默认选项
	options := &Options{
		Config:            nil,
		EnableLog:         true,
		EnableAutoMigrate: false,
	}

	// 应用选项
	for _, opt := range opts {
		opt(options)
	}

	// 检查配置
	if options.Config == nil {
		return fmt.Errorf("数据库配置不能为空")
	}

	// 创建数据库连接
	dialector := sqlserver.Open(options.Config.GetDSN())

	// 配置GORM
	gormConfig := &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // 使用单数表名
		},
		DisableForeignKeyConstraintWhenMigrating: true,                   // 禁用外键约束
		Logger:                                   logger.NewGormLogger(), // 使用自定义日志
	}

	// 创建数据库连接
	db, err := gorm.Open(dialector, gormConfig)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接池失败: %v", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(options.Config.MaxIdleConns)
	sqlDB.SetMaxOpenConns(options.Config.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(options.Config.GetConnMaxLifetime())

	// 设置全局变量
	DB = db

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("测试数据库连接失败: %v", err)
	}

	logger.Info("数据库连接成功", "type", options.Config.Type, "host", options.Config.Host, "database", options.Config.Database)
	return nil
}

// GetDB 获取数据库连接
func GetDB() *gorm.DB {
	return DB
}

// Close 关闭数据库连接
func Close() error {
	if DB == nil {
		return nil
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Close()
}

// Ping 测试数据库连接
func Ping() error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Ping()
}

// InitWithDefault 使用默认配置初始化数据库
func InitWithDefault(dbConfig *config.DBConfig) error {
	return Setup(WithConfig(dbConfig), WithLog(true), WithAutoMigrate(false))
}

// InitWithWosm 使用wosm数据库配置初始化数据库
func InitWithWosm() error {
	dbConfig := &config.DBConfig{
		Type:            "sqlserver",
		Host:            "localhost",
		Port:            1433,
		Username:        "sa",
		Password:        "F@2233",
		Database:        "wosm",
		Params:          "",
		MaxIdleConns:    10,
		MaxOpenConns:    100,
		ConnMaxLifetime: 1,
	}
	return InitWithDefault(dbConfig)
}
