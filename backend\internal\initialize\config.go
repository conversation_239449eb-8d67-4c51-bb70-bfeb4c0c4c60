package initialize

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/ruoyi/backend/internal/config"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// InitConfig 初始化配置
func InitConfig(logger *zap.Logger) (*config.AppConfig, *config.ServerConfig, *config.DBConfig, *config.JWTConfig, *config.RedisCfg, error) {
	// 设置配置文件名
	viper.SetConfigName("config")
	// 设置配置文件类型
	viper.SetConfigType("yaml")
	// 设置配置文件路径
	viper.AddConfigPath("./configs")

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		logger.Error("读取配置文件失败", zap.Error(err))
		return nil, nil, nil, nil, nil, err
	}

	// 获取应用配置
	appConfig := config.NewAppConfig()
	if err := viper.UnmarshalKey("app", appConfig); err != nil {
		logger.Error("解析应用配置失败", zap.Error(err))
		return nil, nil, nil, nil, nil, err
	}

	// 获取服务器配置
	serverConfig := &config.ServerConfig{}
	if err := viper.UnmarshalKey("server", serverConfig); err != nil {
		logger.Error("解析服务器配置失败", zap.Error(err))
		return nil, nil, nil, nil, nil, err
	}

	// 获取数据库配置
	dbConfig := &config.DBConfig{}
	if err := viper.UnmarshalKey("database", dbConfig); err != nil {
		logger.Error("解析数据库配置失败", zap.Error(err))
		return nil, nil, nil, nil, nil, err
	}

	// 获取JWT配置
	jwtConfig := &config.JWTConfig{}
	if err := viper.UnmarshalKey("jwt", jwtConfig); err != nil {
		logger.Error("解析JWT配置失败", zap.Error(err))
		return nil, nil, nil, nil, nil, err
	}

	// 获取Redis配置
	redisConfig := &config.RedisCfg{}
	if err := viper.UnmarshalKey("redis", redisConfig); err != nil {
		logger.Error("解析Redis配置失败", zap.Error(err))
		return nil, nil, nil, nil, nil, err
	}

	// 创建必要的目录
	if err := createDirectories(appConfig, logger); err != nil {
		logger.Error("创建目录失败", zap.Error(err))
		return nil, nil, nil, nil, nil, err
	}

	logger.Info("配置加载成功")
	return appConfig, serverConfig, dbConfig, jwtConfig, redisConfig, nil
}

// createDirectories 创建必要的目录
func createDirectories(appConfig *config.AppConfig, logger *zap.Logger) error {
	dirs := []string{
		appConfig.GetUploadPath(),
		appConfig.GetResourcePath(),
		appConfig.GetAvatarPath(),
		appConfig.GetDownloadPath(),
		appConfig.GetLogPath(),
		appConfig.GetTempPath(),
		"./static",
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			logger.Error("创建目录失败", zap.String("dir", dir), zap.Error(err))
			return fmt.Errorf("创建目录 %s 失败: %v", dir, err)
		}
	}

	// 创建上传目录下的子目录
	uploadSubDirs := []string{"avatar", "document", "image", "import", "media"}
	for _, subDir := range uploadSubDirs {
		path := filepath.Join(appConfig.GetUploadPath(), subDir)
		if err := os.MkdirAll(path, 0755); err != nil {
			logger.Error("创建上传子目录失败", zap.String("dir", path), zap.Error(err))
			return fmt.Errorf("创建上传子目录 %s 失败: %v", path, err)
		}
	}

	return nil
}
