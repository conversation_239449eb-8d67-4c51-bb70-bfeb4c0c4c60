package initialize

import (
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/database"
	"go.uber.org/zap"
)

// InitDatabase 初始化数据库
func InitDatabase(dbConfig *config.DBConfig, logger *zap.Logger) error {
	// 初始化数据库连接
	err := database.Setup(
		database.WithConfig(dbConfig),
		database.WithLog(true),
		database.WithAutoMigrate(false),
	)

	if err != nil {
		logger.Error("初始化数据库失败", zap.Error(err))
		return err
	}

	// 测试数据库连接
	if err := database.Ping(); err != nil {
		logger.Error("测试数据库连接失败", zap.Error(err))
		return err
	}

	logger.Info("数据库初始化成功",
		zap.String("type", dbConfig.Type),
		zap.String("host", dbConfig.Host),
		zap.Int("port", dbConfig.Port),
		zap.String("database", dbConfig.Database),
	)

	return nil
}
