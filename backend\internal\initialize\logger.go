package initialize

import (
	"os"
	"path/filepath"

	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/pkg/logger"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// InitLogger 初始化日志
func InitLogger(appConfig *config.AppConfig) (*zap.Logger, error) {
	// 获取日志配置
	logLevel := viper.GetString("log.level")
	logFormat := viper.GetString("log.format")
	logOutput := viper.GetString("log.output")
	logFilePath := viper.GetString("log.file_path")
	logMaxSize := viper.GetInt("log.max_size")
	logMaxBackups := viper.GetInt("log.max_backups")
	logMaxAge := viper.GetInt("log.max_age")
	logCompress := viper.GetBool("log.compress")
	logCaller := viper.GetBool("log.caller")

	// 如果日志路径不是绝对路径，则使用应用日志路径
	if !filepath.IsAbs(logFilePath) {
		logFilePath = filepath.Join(appConfig.GetLogPath(), logFilePath)
	}

	// 确保日志目录存在
	logDir := filepath.Dir(logFilePath)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return nil, err
	}

	// 创建日志选项
	opts := []logger.Option{
		logger.WithLevel(logLevel),
		logger.WithFormat(logFormat),
		logger.WithMaxSize(logMaxSize),
		logger.WithMaxBackups(logMaxBackups),
		logger.WithMaxAge(logMaxAge),
		logger.WithCompress(logCompress),
		logger.WithCaller(logCaller),
	}

	// 添加输出
	switch logOutput {
	case "console":
		opts = append(opts, logger.WithOutput(zapcore.AddSync(os.Stdout)))
	case "file":
		opts = append(opts, logger.WithOutput(logger.GetFileWriter(logFilePath)))
	case "both":
		opts = append(opts, logger.WithOutput(zapcore.NewMultiWriteSyncer(
			zapcore.AddSync(os.Stdout),
			logger.GetFileWriter(logFilePath),
		)))
	default:
		opts = append(opts, logger.WithOutput(zapcore.AddSync(os.Stdout)))
	}

	// 初始化日志
	zapLogger, err := logger.NewZapLogger(opts...)
	if err != nil {
		return nil, err
	}

	// 替换全局日志
	zap.ReplaceGlobals(zapLogger)

	return zapLogger, nil
}
