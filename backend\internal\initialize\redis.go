package initialize

import (
	"context"
	"fmt"

	"github.com/redis/go-redis/v9"
	"github.com/ruoyi/backend/internal/config"
	myredis "github.com/ruoyi/backend/internal/redis"
	"go.uber.org/zap"
)

// InitRedis 初始化Redis
func InitRedis(redisConfig *config.RedisCfg, logger *zap.Logger) (myredis.RedisService, error) {
	// 创建Redis客户端选项
	options := &redis.Options{
		Addr:         fmt.Sprintf("%s:%d", redisConfig.Host, redisConfig.Port),
		Password:     redisConfig.Password,
		DB:           redisConfig.DB,
		PoolSize:     redisConfig.PoolSize,
		MinIdleConns: redisConfig.MinIdleConns,
		// 使用适当的超时设置
		DialTimeout:  redisConfig.GetDialTimeout(),
		ReadTimeout:  redisConfig.GetReadTimeout(),
		WriteTimeout: redisConfig.GetWriteTimeout(),
	}

	// 创建Redis客户端
	client := redis.NewClient(options)

	// 测试连接
	ctx := context.Background()
	if _, err := client.Ping(ctx).Result(); err != nil {
		logger.Error("Redis连接失败", zap.Error(err))
		return nil, err
	}

	// 创建Redis服务
	redisService := myredis.NewRedisServiceImpl(logger, client)

	logger.Info("Redis初始化成功",
		zap.String("host", redisConfig.Host),
		zap.Int("port", redisConfig.Port),
		zap.Int("db", redisConfig.DB),
		zap.Int("poolSize", redisConfig.PoolSize),
	)

	return redisService, nil
}
