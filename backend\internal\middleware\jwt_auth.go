package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/auth"
	"github.com/ruoyi/backend/internal/model"
	"go.uber.org/zap"
)

// JWTAuthMiddleware JWT认证中间件
func JWTAuthMiddleware(logger *zap.Logger, tokenManager *auth.TokenManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求头中的token
		token := c.GetHeader(auth.TokenHeader)

		// 如果请求头中没有token，则从查询参数中获取
		if token == "" {
			token = c.Query("token")
		}

		// 如果没有token，则返回未授权错误
		if token == "" {
			result := model.AjaxResult{
				Code: http.StatusUnauthorized,
				Msg:  "请求未授权，无法访问系统资源",
			}
			c.AbortWithStatusJSON(http.StatusUnauthorized, result)
			return
		}

		// 如果token不是以Bearer开头，则添加前缀
		if !strings.HasPrefix(token, auth.TokenPrefix) {
			token = auth.TokenPrefix + token
		}

		// 获取登录用户
		loginUser := tokenManager.GetLoginUser(c)
		if loginUser == nil {
			result := model.AjaxResult{
				Code: http.StatusUnauthorized,
				Msg:  "登录状态已过期，请重新登录",
			}
			c.AbortWithStatusJSON(http.StatusUnauthorized, result)
			return
		}

		// 验证token有效期，如果快过期则自动刷新
		tokenManager.VerifyToken(loginUser)

		// 将用户信息保存到上下文中
		c.Set("loginUser", loginUser)

		// 继续处理请求
		c.Next()
	}
}

// GetLoginUser 从上下文中获取登录用户
func GetLoginUser(c *gin.Context) *model.LoginUser {
	value, exists := c.Get("loginUser")
	if !exists {
		return nil
	}

	if loginUser, ok := value.(*model.LoginUser); ok {
		return loginUser
	}

	return nil
}
