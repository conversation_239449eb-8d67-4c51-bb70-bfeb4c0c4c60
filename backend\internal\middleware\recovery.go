package middleware

import (
	"fmt"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/model"
	"go.uber.org/zap"
)

// RecoveryMiddleware 恢复中间件
func RecoveryMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 记录堆栈信息
				stackInfo := string(debug.Stack())
				logger.Error("发生panic",
					zap.Any("error", err),
					zap.String("stack", stackInfo),
					zap.String("path", c.Request.URL.Path),
					zap.String("method", c.Request.Method),
					zap.String("clientIP", c.ClientIP()),
				)

				// 构建错误响应
				errMsg := fmt.Sprintf("系统内部错误: %v", err)
				result := model.AjaxResult{
					Code: http.StatusInternalServerError,
					Msg:  errMsg,
				}

				// 中止请求，返回错误信息
				c.AbortWithStatusJSON(http.StatusInternalServerError, result)
			}
		}()

		// 继续处理请求
		c.Next()
	}
}
