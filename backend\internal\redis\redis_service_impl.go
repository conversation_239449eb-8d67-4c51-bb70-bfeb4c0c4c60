package redis

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// RedisServiceImpl Redis服务实现
type RedisServiceImpl struct {
	logger *zap.Logger
	client *redis.Client
}

// NewRedisServiceImpl 创建Redis服务实现
func NewRedisServiceImpl(logger *zap.Logger, client *redis.Client) RedisService {
	return &RedisServiceImpl{
		logger: logger,
		client: client,
	}
}

// Set 设置缓存
func (s *RedisServiceImpl) Set(key string, value string, timeout time.Duration) error {
	ctx := context.Background()
	err := s.client.Set(ctx, key, value, timeout).Err()
	if err != nil {
		s.logger.Error("Redis设置缓存失败", zap.String("key", key), zap.Error(err))
		return err
	}
	return nil
}

// Get 获取缓存
func (s *RedisServiceImpl) Get(key string) (string, error) {
	ctx := context.Background()
	val, err := s.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			// 键不存在
			return "", nil
		}
		s.logger.Error("Redis获取缓存失败", zap.String("key", key), zap.Error(err))
		return "", err
	}
	return val, nil
}

// Del 删除缓存
func (s *RedisServiceImpl) Del(key string) error {
	ctx := context.Background()
	err := s.client.Del(ctx, key).Err()
	if err != nil {
		s.logger.Error("Redis删除缓存失败", zap.String("key", key), zap.Error(err))
		return err
	}
	return nil
}

// Exists 判断key是否存在
func (s *RedisServiceImpl) Exists(key string) (bool, error) {
	ctx := context.Background()
	val, err := s.client.Exists(ctx, key).Result()
	if err != nil {
		s.logger.Error("Redis判断key是否存在失败", zap.String("key", key), zap.Error(err))
		return false, err
	}
	return val > 0, nil
}

// Expire 设置过期时间
func (s *RedisServiceImpl) Expire(key string, timeout time.Duration) error {
	ctx := context.Background()
	err := s.client.Expire(ctx, key, timeout).Err()
	if err != nil {
		s.logger.Error("Redis设置过期时间失败", zap.String("key", key), zap.Error(err))
		return err
	}
	return nil
}
