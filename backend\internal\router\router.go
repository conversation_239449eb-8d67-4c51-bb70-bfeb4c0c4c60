package router

import (
	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/config"
	"github.com/ruoyi/backend/internal/middleware"
	"go.uber.org/zap"
)

// InitRouter 初始化路由
func InitRouter(engine *gin.Engine, logger *zap.Logger, appConfig *config.AppConfig) {
	// 设置路由模式
	if appConfig.IsProd() {
		gin.SetMode(gin.ReleaseMode)
	} else if appConfig.IsTest() {
		gin.SetMode(gin.TestMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	// 注册全局中间件
	registerGlobalMiddleware(engine, logger)

	// 注册静态资源路由
	registerStaticRoutes(engine)

	// 创建API路由组
	apiGroup := engine.Group(appConfig.Server.ContextPath)

	// 注册系统路由
	RegisterSystemRoutes(apiGroup, logger)

	// 注册监控路由
	RegisterMonitorRoutes(apiGroup, logger)

	// 注册通用路由
	RegisterCommonRoutes(apiGroup, logger)

	// 注册404处理
	engine.NoRoute(func(c *gin.Context) {
		c.JSON(404, gin.H{
			"code": 404,
			"msg":  "Not Found",
		})
	})
}

// registerGlobalMiddleware 注册全局中间件
func registerGlobalMiddleware(engine *gin.Engine, logger *zap.Logger) {
	// 恢复中间件
	engine.Use(middleware.RecoveryMiddleware(logger))

	// 日志中间件
	engine.Use(middleware.LoggerMiddleware(logger))

	// CORS中间件
	engine.Use(middleware.CorsMiddleware())

	// XSS过滤中间件
	engine.Use(middleware.XssMiddleware(logger))
}

// registerStaticRoutes 注册静态资源路由
func registerStaticRoutes(engine *gin.Engine) {
	// 静态资源目录
	engine.Static("/static", "./static")
	engine.Static("/profile", "./profile")
}
