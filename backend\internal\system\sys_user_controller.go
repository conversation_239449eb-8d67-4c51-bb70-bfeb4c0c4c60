package system

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/model"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
)

// SysUserController 用户信息控制器
type SysUserController struct {
	controller.BaseController
	userService service.ISysUserService
	roleService service.ISysRoleService
	postService service.ISysPostService
	deptService service.ISysDeptService
}

// NewSysUserController 创建用户控制器
func NewSysUserController(
	logger *zap.Logger,
	userService service.ISysUserService,
	roleService service.ISysRoleService,
	postService service.ISysPostService,
	deptService service.ISysDeptService,
) *SysUserController {
	return &SysUserController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		userService: userService,
		roleService: roleService,
		postService: postService,
		deptService: deptService,
	}
}

// RegisterRoutes 注册路由
func (c *SysUserController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)
	r.POST("/export", c.Export)
	r.POST("/importData", c.ImportData)
	r.POST("/importTemplate", c.ImportTemplate)
	r.GET("/:userId", c.GetInfo)
	r.GET("/", c.GetInfo)
	r.POST("", c.Add)
	r.PUT("", c.Edit)
	r.DELETE("/:userIds", c.Remove)
	r.PUT("/resetPwd", c.ResetPwd)
	r.PUT("/changeStatus", c.ChangeStatus)
	r.GET("/authRole/:userId", c.AuthRole)
	r.PUT("/authRole", c.InsertAuthRole)
	r.GET("/deptTree", c.DeptTree)
}

// List 获取用户列表
func (c *SysUserController) List(ctx *gin.Context) {
	c.StartPage(ctx)

	// 构建查询条件
	user := &domain.SysUser{}
	// 从请求中绑定参数
	// TODO: 实现参数绑定

	// 调用服务获取用户列表
	list, total := c.userService.SelectUserList(user)

	// 返回结果
	ctx.JSON(http.StatusOK, c.GetDataTable(ctx, list, total))
}

// Export 导出用户
func (c *SysUserController) Export(ctx *gin.Context) {
	// 构建查询条件
	user := &domain.SysUser{}
	// 从请求中绑定参数
	// TODO: 实现参数绑定

	// 调用服务获取用户列表
	list, _ := c.userService.SelectUserList(user)

	// TODO: 实现Excel导出
	c.SuccessWithMessage(ctx, "导出成功")
}

// ImportData 导入数据
func (c *SysUserController) ImportData(ctx *gin.Context) {
	file, _, err := ctx.Request.FormFile("file")
	if err != nil {
		c.ErrorWithMessage(ctx, "获取上传文件失败")
		return
	}

	updateSupport := ctx.DefaultPostForm("updateSupport", "false") == "true"

	// TODO: 实现Excel导入
	// 调用服务导入用户
	message := "导入成功"

	c.SuccessWithMessage(ctx, message)
}

// ImportTemplate 导入模板
func (c *SysUserController) ImportTemplate(ctx *gin.Context) {
	// TODO: 实现导出模板
	c.SuccessWithMessage(ctx, "导出模板成功")
}

// GetInfo 根据用户编号获取详细信息
func (c *SysUserController) GetInfo(ctx *gin.Context) {
	userIdStr := ctx.Param("userId")
	result := model.AjaxResult{}

	if userIdStr != "" {
		userId, err := strconv.ParseInt(userIdStr, 10, 64)
		if err != nil {
			c.ErrorWithMessage(ctx, "用户ID格式错误")
			return
		}

		// 检查数据权限
		c.userService.CheckUserDataScope(userId)

		// 获取用户信息
		user := c.userService.SelectUserById(userId)

		// 获取岗位
		postIds := c.postService.SelectPostListByUserId(userId)

		// 获取角色
		roleIds := c.roleService.SelectRoleListByUserId(userId)

		result = model.AjaxResult{
			Code: http.StatusOK,
			Msg:  "操作成功",
			Data: map[string]interface{}{
				"data":    user,
				"postIds": postIds,
				"roleIds": roleIds,
			},
		}
	}

	// 获取所有角色
	roles := c.roleService.SelectRoleAll()

	// 获取所有岗位
	posts := c.postService.SelectPostAll()

	// 如果result.Data为nil，初始化它
	if result.Data == nil {
		result.Data = make(map[string]interface{})
	}

	// 将角色和岗位添加到result.Data中
	if dataMap, ok := result.Data.(map[string]interface{}); ok {
		dataMap["roles"] = roles
		dataMap["posts"] = posts
	}

	ctx.JSON(http.StatusOK, result)
}

// Add 新增用户
func (c *SysUserController) Add(ctx *gin.Context) {
	var user domain.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查部门数据权限
	c.deptService.CheckDeptDataScope(user.DeptId)

	// 检查角色数据权限
	c.roleService.CheckRoleDataScope(user.RoleIds)

	// 校验用户名唯一性
	if !c.userService.CheckUserNameUnique(&user) {
		c.ErrorWithMessage(ctx, "新增用户'"+user.UserName+"'失败，登录账号已存在")
		return
	}

	// 校验手机号唯一性
	if user.Phonenumber != "" && !c.userService.CheckPhoneUnique(&user) {
		c.ErrorWithMessage(ctx, "新增用户'"+user.UserName+"'失败，手机号码已存在")
		return
	}

	// 校验邮箱唯一性
	if user.Email != "" && !c.userService.CheckEmailUnique(&user) {
		c.ErrorWithMessage(ctx, "新增用户'"+user.UserName+"'失败，邮箱账号已存在")
		return
	}

	// 设置创建者
	user.CreateBy = c.GetUsername(ctx)

	// 加密密码
	// TODO: 实现密码加密

	// 新增用户
	rows := c.userService.InsertUser(&user)
	c.ToAjax(ctx, rows)
}

// Edit 修改用户
func (c *SysUserController) Edit(ctx *gin.Context) {
	var user domain.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查是否允许操作用户
	c.userService.CheckUserAllowed(&user)

	// 检查用户数据权限
	c.userService.CheckUserDataScope(user.UserId)

	// 检查部门数据权限
	c.deptService.CheckDeptDataScope(user.DeptId)

	// 检查角色数据权限
	c.roleService.CheckRoleDataScope(user.RoleIds)

	// 校验用户名唯一性
	if !c.userService.CheckUserNameUnique(&user) {
		c.ErrorWithMessage(ctx, "修改用户'"+user.UserName+"'失败，登录账号已存在")
		return
	}

	// 校验手机号唯一性
	if user.Phonenumber != "" && !c.userService.CheckPhoneUnique(&user) {
		c.ErrorWithMessage(ctx, "修改用户'"+user.UserName+"'失败，手机号码已存在")
		return
	}

	// 校验邮箱唯一性
	if user.Email != "" && !c.userService.CheckEmailUnique(&user) {
		c.ErrorWithMessage(ctx, "修改用户'"+user.UserName+"'失败，邮箱账号已存在")
		return
	}

	// 设置更新者
	user.UpdateBy = c.GetUsername(ctx)

	// 更新用户
	rows := c.userService.UpdateUser(&user)
	c.ToAjax(ctx, rows)
}

// Remove 删除用户
func (c *SysUserController) Remove(ctx *gin.Context) {
	userIdsStr := ctx.Param("userIds")
	userIds := strings.Split(userIdsStr, ",")

	// 将字符串ID转换为int64数组
	ids := make([]int64, 0, len(userIds))
	for _, idStr := range userIds {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}

		// 不能删除当前用户
		if id == c.GetUserId(ctx) {
			c.ErrorWithMessage(ctx, "当前用户不能删除")
			return
		}

		ids = append(ids, id)
	}

	// 删除用户
	rows := c.userService.DeleteUserByIds(ids)
	c.ToAjax(ctx, rows)
}

// ResetPwd 重置密码
func (c *SysUserController) ResetPwd(ctx *gin.Context) {
	var user domain.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查是否允许操作用户
	c.userService.CheckUserAllowed(&user)

	// 检查用户数据权限
	c.userService.CheckUserDataScope(user.UserId)

	// 加密密码
	// TODO: 实现密码加密

	// 设置更新者
	user.UpdateBy = c.GetUsername(ctx)

	// 重置密码
	rows := c.userService.ResetPwd(&user)
	c.ToAjax(ctx, rows)
}

// ChangeStatus 状态修改
func (c *SysUserController) ChangeStatus(ctx *gin.Context) {
	var user domain.SysUser
	if err := ctx.ShouldBindJSON(&user); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查是否允许操作用户
	c.userService.CheckUserAllowed(&user)

	// 检查用户数据权限
	c.userService.CheckUserDataScope(user.UserId)

	// 设置更新者
	user.UpdateBy = c.GetUsername(ctx)

	// 修改用户状态
	rows := c.userService.UpdateUserStatus(&user)
	c.ToAjax(ctx, rows)
}

// AuthRole 根据用户编号获取授权角色
func (c *SysUserController) AuthRole(ctx *gin.Context) {
	userIdStr := ctx.Param("userId")
	userId, err := strconv.ParseInt(userIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "用户ID格式错误")
		return
	}

	// 获取用户信息
	user := c.userService.SelectUserById(userId)

	// 获取用户角色
	roles := c.roleService.SelectRolesByUserId(userId)

	c.SuccessWithData(ctx, map[string]interface{}{
		"user":  user,
		"roles": roles,
	})
}

// InsertAuthRole 用户授权角色
func (c *SysUserController) InsertAuthRole(ctx *gin.Context) {
	userIdStr := ctx.PostForm("userId")
	roleIdsStr := ctx.PostForm("roleIds")

	userId, err := strconv.ParseInt(userIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "用户ID格式错误")
		return
	}

	// 检查用户数据权限
	c.userService.CheckUserDataScope(userId)

	// 将角色ID字符串转换为int64数组
	roleIds := make([]int64, 0)
	if roleIdsStr != "" {
		for _, idStr := range strings.Split(roleIdsStr, ",") {
			id, err := strconv.ParseInt(idStr, 10, 64)
			if err != nil {
				continue
			}
			roleIds = append(roleIds, id)
		}
	}

	// 检查角色数据权限
	c.roleService.CheckRoleDataScope(roleIds)

	// 授权角色
	c.userService.InsertUserAuth(userId, roleIds)
	c.Success(ctx)
}

// DeptTree 获取部门树列表
func (c *SysUserController) DeptTree(ctx *gin.Context) {
	var dept domain.SysDept
	// 从请求中绑定参数
	// TODO: 实现参数绑定

	// 获取部门树
	deptTree := c.deptService.SelectDeptTreeList(&dept)
	c.SuccessWithData(ctx, deptTree)
}
