package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/ruoyi/backend/internal/auth"
	"github.com/ruoyi/backend/internal/database"
	"github.com/ruoyi/backend/internal/initialize"
	"go.uber.org/zap"
)

func main() {
	// 创建基础日志
	baseLogger, _ := zap.NewProduction()
	defer baseLogger.Sync()
	zap.ReplaceGlobals(baseLogger)

	baseLogger.Info("服务启动中...")

	// 初始化配置
	appConfig, serverConfig, dbConfig, jwtConfig, redisConfig, err := initialize.InitConfig(baseLogger)
	if err != nil {
		baseLogger.Fatal("初始化配置失败", zap.Error(err))
		return
	}

	// 初始化日志
	logger, err := initialize.InitLogger(appConfig)
	if err != nil {
		baseLogger.Fatal("初始化日志失败", zap.Error(err))
		return
	}
	defer logger.Sync()

	// 初始化数据库
	if err := initialize.InitDatabase(dbConfig, logger); err != nil {
		logger.Fatal("初始化数据库失败", zap.Error(err))
		return
	}
	defer database.Close()

	// 初始化Redis
	redisService, err := initialize.InitRedis(redisConfig, logger)
	if err != nil {
		logger.Fatal("初始化Redis失败", zap.Error(err))
		return
	}

	// 初始化JWT
	if err := auth.InitJWT(jwtConfig); err != nil {
		logger.Fatal("初始化JWT失败", zap.Error(err))
		return
	}

	// 初始化路由
	engine := initialize.InitRouter(appConfig, logger)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:           fmt.Sprintf("%s:%d", serverConfig.Host, serverConfig.Port),
		Handler:        engine,
		ReadTimeout:    serverConfig.GetReadTimeout(),
		WriteTimeout:   serverConfig.GetWriteTimeout(),
		IdleTimeout:    serverConfig.GetIdleTimeout(),
		MaxHeaderBytes: 1 << 20, // 1 MB
	}

	// 启动HTTP服务器
	go func() {
		logger.Info("正在启动HTTP服务器...",
			zap.String("地址", server.Addr),
			zap.String("环境", appConfig.Env),
		)

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("启动服务器失败", zap.Error(err))
		}
	}()

	// 输出服务启动成功信息
	logger.Info("服务器启动成功",
		zap.String("地址", server.Addr),
		zap.String("版本", appConfig.Version),
		zap.String("环境", appConfig.Env),
	)

	// ASCII艺术输出，仿照Java项目
	fmt.Println(`
    ____                  __   ______        
   / __ \__  ____  __  __/ /_ / ____/___     
  / /_/ / / / / / / / / / __ \/ / __/ __ \   
 / _, _/ /_/ / /_/ / /_/ / /_/ /_/ / /_/ /   
/_/ |_|\__,_/\__, /\__,_/\__/\____/\____/    
            /____/                           
`)

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("正在关闭服务器...")

	// 清理缓存
	ctx := context.Background()
	if err := redisService.FlushDB(ctx); err != nil {
		logger.Error("清理Redis缓存失败", zap.Error(err))
	}

	// 创建5秒超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 优雅关闭服务器
	if err := server.Shutdown(ctx); err != nil {
		logger.Fatal("服务器关闭失败", zap.Error(err))
	}

	logger.Info("服务器已关闭")
}
