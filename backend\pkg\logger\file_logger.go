package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// FileLoggerOptions 文件日志选项
type FileLoggerOptions struct {
	// Filename 日志文件路径
	Filename string
	// MaxSize 日志文件最大大小，单位MB
	MaxSize int
	// MaxBackups 最大备份数量
	MaxBackups int
	// MaxAge 最大保存天数
	MaxAge int
	// Compress 是否压缩
	Compress bool
	// Level 日志级别
	Level LogLevel
	// Format 日志格式 (json or console)
	Format string
	// TimeFormat 时间格式
	TimeFormat string
}

// NewFileLogger 创建一个新的文件日志记录器
func NewFileLogger(opts FileLoggerOptions) Logger {
	// 设置默认值
	if opts.MaxSize == 0 {
		opts.MaxSize = 100
	}
	if opts.MaxBackups == 0 {
		opts.MaxBackups = 10
	}
	if opts.MaxAge == 0 {
		opts.MaxAge = 30
	}
	if opts.Format == "" {
		opts.Format = "json"
	}
	if opts.TimeFormat == "" {
		opts.TimeFormat = "2006-01-02 15:04:05"
	}

	// 确保目录存在
	dir := filepath.Dir(opts.Filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		panic(fmt.Sprintf("无法创建日志目录: %v", err))
	}

	// 创建日志轮转器
	fileWriter := &lumberjack.Logger{
		Filename:   opts.Filename,
		MaxSize:    opts.MaxSize,
		MaxBackups: opts.MaxBackups,
		MaxAge:     opts.MaxAge,
		Compress:   opts.Compress,
	}

	// 创建编码器配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.TimeEncoderOfLayout(opts.TimeFormat),
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 创建编码器
	var encoder zapcore.Encoder
	if opts.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// 创建核心
	core := zapcore.NewCore(
		encoder,
		zapcore.AddSync(fileWriter),
		zap.NewAtomicLevelAt(zapcore.Level(opts.Level-1)), // zap的级别比我们定义的低1
	)

	// 创建zap logger
	logger := zap.New(
		core,
		zap.AddCaller(),
		zap.AddCallerSkip(1),
	)

	return &zapLogger{
		logger: logger.Sugar(),
	}
}

// NewRotateFileLogger 创建一个按日期轮转的文件日志记录器
func NewRotateFileLogger(baseDir string, opts ...Option) Logger {
	options := &LoggerOptions{
		Config: LogConfig{
			Level:             InfoLevel,
			Format:            "json",
			Development:       false,
			DisableCaller:     false,
			DisableStacktrace: false,
			TimeFormat:        "2006-01-02 15:04:05",
		},
	}

	// 应用选项
	for _, opt := range opts {
		opt(options)
	}

	// 确保目录存在
	if err := os.MkdirAll(baseDir, 0755); err != nil {
		panic(fmt.Sprintf("无法创建日志目录: %v", err))
	}

	// 创建当前日期的日志文件
	now := time.Now()
	filename := filepath.Join(baseDir, fmt.Sprintf("%s.log", now.Format("2006-01-02")))

	// 创建文件日志记录器
	return NewFileLogger(FileLoggerOptions{
		Filename:   filename,
		MaxSize:    100,
		MaxBackups: 10,
		MaxAge:     30,
		Compress:   true,
		Level:      options.Config.Level,
		Format:     options.Config.Format,
		TimeFormat: options.Config.TimeFormat,
	})
}

// NewDailyFileLogger 创建一个每日轮转的文件日志记录器
func NewDailyFileLogger(baseDir, appName string, opts ...Option) Logger {
	options := &LoggerOptions{
		Config: LogConfig{
			Level:             InfoLevel,
			Format:            "json",
			Development:       false,
			DisableCaller:     false,
			DisableStacktrace: false,
			TimeFormat:        "2006-01-02 15:04:05",
		},
	}

	// 应用选项
	for _, opt := range opts {
		opt(options)
	}

	// 确保目录存在
	logDir := filepath.Join(baseDir, appName, "logs")
	if err := os.MkdirAll(logDir, 0755); err != nil {
		panic(fmt.Sprintf("无法创建日志目录: %v", err))
	}

	// 创建当前日期的日志文件
	now := time.Now()
	filename := filepath.Join(logDir, fmt.Sprintf("%s_%s.log", appName, now.Format("2006-01-02")))

	// 创建文件日志记录器
	return NewFileLogger(FileLoggerOptions{
		Filename:   filename,
		MaxSize:    100,
		MaxBackups: 10,
		MaxAge:     30,
		Compress:   true,
		Level:      options.Config.Level,
		Format:     options.Config.Format,
		TimeFormat: options.Config.TimeFormat,
	})
}
