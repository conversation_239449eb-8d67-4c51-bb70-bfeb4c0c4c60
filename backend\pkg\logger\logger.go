package logger

import (
	"context"
)

// Logger 日志接口
type Logger interface {
	// Debug 输出调试级别的日志
	Debug(args ...interface{})
	// Debugf 输出调试级别的格式化日志
	Debugf(format string, args ...interface{})
	// Debugw 输出调试级别的键值对日志
	Debugw(msg string, keysAndValues ...interface{})

	// Info 输出信息级别的日志
	Info(args ...interface{})
	// Infof 输出信息级别的格式化日志
	Infof(format string, args ...interface{})
	// Infow 输出信息级别的键值对日志
	Infow(msg string, keysAndValues ...interface{})

	// Warn 输出警告级别的日志
	Warn(args ...interface{})
	// Warnf 输出警告级别的格式化日志
	Warnf(format string, args ...interface{})
	// Warnw 输出警告级别的键值对日志
	Warnw(msg string, keysAndValues ...interface{})

	// Error 输出错误级别的日志
	Error(args ...interface{})
	// Errorf 输出错误级别的格式化日志
	Errorf(format string, args ...interface{})
	// Errorw 输出错误级别的键值对日志
	Errorw(msg string, keysAndValues ...interface{})

	// Fatal 输出致命级别的日志
	Fatal(args ...interface{})
	// Fatalf 输出致命级别的格式化日志
	Fatalf(format string, args ...interface{})
	// Fatalw 输出致命级别的键值对日志
	Fatalw(msg string, keysAndValues ...interface{})

	// WithContext 创建一个带有上下文的日志记录器
	WithContext(ctx context.Context) Logger

	// WithValues 创建一个带有键值对的日志记录器
	WithValues(keysAndValues ...interface{}) Logger

	// WithName 创建一个带有名称的日志记录器
	WithName(name string) Logger

	// Sync 同步缓冲区数据到输出端
	Sync() error
}

// LogLevel 日志级别
type LogLevel int

const (
	// DebugLevel 调试级别
	DebugLevel LogLevel = iota
	// InfoLevel 信息级别
	InfoLevel
	// WarnLevel 警告级别
	WarnLevel
	// ErrorLevel 错误级别
	ErrorLevel
	// FatalLevel 致命级别
	FatalLevel
)

// LogConfig 日志配置
type LogConfig struct {
	// Level 日志级别
	Level LogLevel `json:"level" yaml:"level"`
	// Format 日志格式 (json or console)
	Format string `json:"format" yaml:"format"`
	// OutputPaths 日志输出路径
	OutputPaths []string `json:"output_paths" yaml:"output_paths"`
	// ErrorOutputPaths 错误日志输出路径
	ErrorOutputPaths []string `json:"error_output_paths" yaml:"error_output_paths"`
	// Development 是否为开发模式
	Development bool `json:"development" yaml:"development"`
	// DisableCaller 是否禁用调用者信息
	DisableCaller bool `json:"disable_caller" yaml:"disable_caller"`
	// DisableStacktrace 是否禁用堆栈跟踪
	DisableStacktrace bool `json:"disable_stacktrace" yaml:"disable_stacktrace"`
	// TimeFormat 时间格式
	TimeFormat string `json:"time_format" yaml:"time_format"`
}

// LoggerOptions 日志选项
type LoggerOptions struct {
	// Config 日志配置
	Config LogConfig
}

// Option 日志选项函数
type Option func(*LoggerOptions)

// WithLogLevel 设置日志级别
func WithLogLevel(level LogLevel) Option {
	return func(o *LoggerOptions) {
		o.Config.Level = level
	}
}

// WithLogFormat 设置日志格式
func WithLogFormat(format string) Option {
	return func(o *LoggerOptions) {
		o.Config.Format = format
	}
}

// WithOutputPaths 设置日志输出路径
func WithOutputPaths(paths ...string) Option {
	return func(o *LoggerOptions) {
		o.Config.OutputPaths = paths
	}
}

// WithErrorOutputPaths 设置错误日志输出路径
func WithErrorOutputPaths(paths ...string) Option {
	return func(o *LoggerOptions) {
		o.Config.ErrorOutputPaths = paths
	}
}

// WithDevelopment 设置是否为开发模式
func WithDevelopment(development bool) Option {
	return func(o *LoggerOptions) {
		o.Config.Development = development
	}
}

// WithDisableCaller 设置是否禁用调用者信息
func WithDisableCaller(disable bool) Option {
	return func(o *LoggerOptions) {
		o.Config.DisableCaller = disable
	}
}

// WithDisableStacktrace 设置是否禁用堆栈跟踪
func WithDisableStacktrace(disable bool) Option {
	return func(o *LoggerOptions) {
		o.Config.DisableStacktrace = disable
	}
}

// WithTimeFormat 设置时间格式
func WithTimeFormat(format string) Option {
	return func(o *LoggerOptions) {
		o.Config.TimeFormat = format
	}
}

// New 创建一个新的日志记录器
var New func(opts ...Option) Logger
