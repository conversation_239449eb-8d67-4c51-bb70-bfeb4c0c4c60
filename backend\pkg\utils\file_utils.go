package utils

import (
	"io"
	"io/ioutil"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
)

// FileInfo 文件信息
type FileInfo struct {
	Name      string `json:"name"`      // 文件名称
	Path      string `json:"path"`      // 文件路径
	Size      int64  `json:"size"`      // 文件大小
	Extension string `json:"extension"` // 文件扩展名
	IsDir     bool   `json:"isDir"`     // 是否是目录
}

// FileExists 判断文件是否存在
func FileExists(path string) bool {
	_, err := os.Stat(path)
	if err != nil {
		if os.IsNotExist(err) {
			return false
		}
	}
	return true
}

// IsDir 判断是否是目录
func IsDir(path string) bool {
	s, err := os.Stat(path)
	if err != nil {
		return false
	}
	return s.IsDir()
}

// IsFile 判断是否是文件
func IsFile(path string) bool {
	return FileExists(path) && !IsDir(path)
}

// CreateDir 创建目录
func CreateDir(path string) error {
	return os.MkdirAll(path, os.ModePerm)
}

// DeleteFile 删除文件
func DeleteFile(path string) error {
	return os.Remove(path)
}

// DeleteDir 删除目录
func DeleteDir(path string) error {
	return os.RemoveAll(path)
}

// GetFileSize 获取文件大小
func GetFileSize(path string) int64 {
	info, err := os.Stat(path)
	if err != nil {
		return 0
	}
	return info.Size()
}

// GetFileExt 获取文件扩展名
func GetFileExt(filename string) string {
	return filepath.Ext(filename)
}

// GetFileName 获取文件名
func GetFileName(path string) string {
	return filepath.Base(path)
}

// GetFileNameWithoutExt 获取文件名（不含扩展名）
func GetFileNameWithoutExt(filename string) string {
	ext := filepath.Ext(filename)
	return filename[0 : len(filename)-len(ext)]
}

// GetFilePath 获取文件路径
func GetFilePath(path string) string {
	return filepath.Dir(path)
}

// ReadFile 读取文件内容
func ReadFile(path string) ([]byte, error) {
	return ioutil.ReadFile(path)
}

// WriteFile 写入文件内容
func WriteFile(path string, data []byte) error {
	dir := filepath.Dir(path)
	if !FileExists(dir) {
		err := CreateDir(dir)
		if err != nil {
			return err
		}
	}
	return ioutil.WriteFile(path, data, 0644)
}

// AppendFile 追加文件内容
func AppendFile(path string, data []byte) error {
	file, err := os.OpenFile(path, os.O_WRONLY|os.O_APPEND|os.O_CREATE, 0644)
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = file.Write(data)
	return err
}

// CopyFile 复制文件
func CopyFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	return err
}

// GetAllFiles 获取目录下所有文件
func GetAllFiles(dirPath string) ([]FileInfo, error) {
	var files []FileInfo

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		files = append(files, FileInfo{
			Name:      info.Name(),
			Path:      path,
			Size:      info.Size(),
			Extension: filepath.Ext(info.Name()),
			IsDir:     info.IsDir(),
		})

		return nil
	})

	return files, err
}

// GetFiles 获取目录下的文件
func GetFiles(dirPath string) ([]FileInfo, error) {
	var files []FileInfo

	fileInfos, err := ioutil.ReadDir(dirPath)
	if err != nil {
		return nil, err
	}

	for _, info := range fileInfos {
		files = append(files, FileInfo{
			Name:      info.Name(),
			Path:      filepath.Join(dirPath, info.Name()),
			Size:      info.Size(),
			Extension: filepath.Ext(info.Name()),
			IsDir:     info.IsDir(),
		})
	}

	return files, nil
}

// SaveUploadedFile 保存上传的文件
func SaveUploadedFile(file *multipart.FileHeader, dst string) error {
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	// 创建目标文件
	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	// 拷贝文件内容
	_, err = io.Copy(out, src)
	return err
}

// IsAllowedFileType 判断文件类型是否允许
func IsAllowedFileType(filename string, allowedExts []string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	if ext == "" {
		return false
	}

	// 如果ext以.开头，去掉.
	if ext[0] == '.' {
		ext = ext[1:]
	}

	for _, allowedExt := range allowedExts {
		allowedExt = strings.ToLower(allowedExt)
		// 如果allowedExt以.开头，去掉.
		if len(allowedExt) > 0 && allowedExt[0] == '.' {
			allowedExt = allowedExt[1:]
		}

		if ext == allowedExt {
			return true
		}
	}

	return false
}
