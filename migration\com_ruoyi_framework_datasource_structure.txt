# Package: com.ruoyi.framework.datasource

## Class: DynamicDataSource

Extends: AbstractRoutingDataSource

### Fields:

### Methods:
- Object determineCurrentLookupKey() (Override)

### Go Implementation Suggestion:
```go
package datasource

type DynamicDataSource struct {
}

func (c *DynamicDataSource) determineCurrentLookupKey() interface{} {
	// TODO: Implement method
	return nil
}

```

## Class: DynamicDataSourceContextHolder

### Fields:
- Logger log
- ThreadLocal<String> CONTEXT_HOLDER

### Methods:
- void setDataSourceType(String dsType)
- String getDataSourceType()
- void clearDataSourceType()

### Go Implementation Suggestion:
```go
package datasource

type DynamicDataSourceContextHolder struct {
	Log Logger
	CONTEXT_HOLDER ThreadLocal<String>
}

func (c *DynamicDataSourceContextHolder) setDataSourceType(dsType string) {
	// TODO: Implement method
}

func (c *DynamicDataSourceContextHolder) getDataSourceType() string {
	// TODO: Implement method
	return ""
}

func (c *DynamicDataSourceContextHolder) clearDataSourceType() {
	// TODO: Implement method
}

```

