# Package: com.ruoyi.framework.interceptor.impl

## Class: SameUrlDataInterceptor

Extends: RepeatSubmitInterceptor

### Fields:
- String REPEAT_PARAMS
- String REPEAT_TIME
- String header (Value)
- RedisCache redisCache (Autowired)

### Methods:
- boolean isRepeatSubmit(HttpServletRequest request, RepeatSubmit annotation) (SuppressWarnings, Override)
- boolean compareParams(Map<String, Object> nowMap, Map<String, Object> preMap)
- boolean compareTime(Map<String, Object> nowMap, Map<String, Object> preMap, int interval)

### Go Implementation Suggestion:
```go
package impl

type SameUrlDataInterceptor struct {
	REPEAT_PARAMS string
	REPEAT_TIME string
	Header string
	RedisCache RedisCache
}

func (c *SameUrlDataInterceptor) isRepeatSubmit(request HttpServletRequest, annotation RepeatSubmit) bool {
	// TODO: Implement method
	return false
}

func (c *SameUrlDataInterceptor) compareParams(Object> map[string]interface{}, Object> map[string]interface{}) bool {
	// TODO: Implement method
	return false
}

func (c *SameUrlDataInterceptor) compareTime(Object> map[string]interface{}, Object> map[string]interface{}, interval int) bool {
	// TODO: Implement method
	return false
}

```

