# Package: com.ruoyi.framework.interceptor

## Class: RepeatSubmitInterceptor

Implements: HandlerInterceptor

### Fields:

### Methods:
- boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) (Override)
- boolean isRepeatSubmit(HttpServletRequest request, RepeatSubmit annotation)

### Go Implementation Suggestion:
```go
package interceptor

type RepeatSubmitInterceptor struct {
}

func (c *RepeatSubmitInterceptor) preHandle(request HttpServletRequest, response HttpServletResponse, handler interface{}) bool {
	// TODO: Implement method
	return false
}

func (c *RepeatSubmitInterceptor) isRepeatSubmit(request HttpServletRequest, annotation RepeatSubmit) bool {
	// TODO: Implement method
	return false
}

```

