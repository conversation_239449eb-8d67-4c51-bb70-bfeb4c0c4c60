# Package: com.ruoyi.framework.manager.factory

## Class: AsyncFactory

### Fields:
- Logger sys_user_logger

### Methods:
- TimerTask recordLogininfor(String username, String status, String message, Object args)
- TimerTask recordOper(SysOperLog operLog)

### Go Implementation Suggestion:
```go
package factory

type AsyncFactory struct {
	Sys_user_logger Logger
}

func (c *AsyncFactory) recordLogininfor(username string, status string, message string, args interface{}) TimerTask {
	// TODO: Implement method
	return nil
}

func (c *AsyncFactory) recordOper(operLog SysOperLog) TimerTask {
	// TODO: Implement method
	return nil
}

```

