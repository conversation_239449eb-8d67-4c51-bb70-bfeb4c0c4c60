# Package: com.ruoyi.framework.security.context

## Class: AuthenticationContextHolder

### Fields:
- ThreadLocal<Authentication> contextHolder

### Methods:
- Authentication getContext()
- void setContext(Authentication context)
- void clearContext()

### Go Implementation Suggestion:
```go
package context

type AuthenticationContextHolder struct {
	ContextHolder ThreadLocal<Authentication>
}

func (c *AuthenticationContextHolder) getContext() Authentication {
	// TODO: Implement method
	return nil
}

func (c *AuthenticationContextHolder) setContext(context Authentication) {
	// TODO: Implement method
}

func (c *AuthenticationContextHolder) clearContext() {
	// TODO: Implement method
}

```

## Class: PermissionContextHolder

### Fields:
- String PERMISSION_CONTEXT_ATTRIBUTES

### Methods:
- void setContext(String permission)
- String getContext()

### Go Implementation Suggestion:
```go
package context

type PermissionContextHolder struct {
	PERMISSION_CONTEXT_ATTRIBUTES string
}

func (c *PermissionContextHolder) setContext(permission string) {
	// TODO: Implement method
}

func (c *PermissionContextHolder) getContext() string {
	// TODO: Implement method
	return ""
}

```

