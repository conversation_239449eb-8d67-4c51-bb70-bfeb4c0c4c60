# Package: com.ruoyi.framework.security.filter

## Class: JwtAuthenticationTokenFilter

Extends: OncePerRequestFilter

### Fields:
- TokenService tokenService (Autowired)

### Methods:
- void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) (Override)

### Go Implementation Suggestion:
```go
package filter

type JwtAuthenticationTokenFilter struct {
	TokenService TokenService
}

func (c *JwtAuthenticationTokenFilter) doFilterInternal(request HttpServletRequest, response HttpServletResponse, chain FilterChain) {
	// TODO: Implement method
}

```

