# Package: com.ruoyi.framework.security.handle

## Class: AuthenticationEntryPointImpl

Implements: AuthenticationEntryPoint, Serializable

### Fields:
- long serialVersionUID

### Methods:
- void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) (Override)

### Go Implementation Suggestion:
```go
package handle

type AuthenticationEntryPointImpl struct {
	SerialVersionUID int64
}

func (c *AuthenticationEntryPointImpl) commence(request HttpServletRequest, response HttpServletResponse, e AuthenticationException) {
	// TODO: Implement method
}

```

## Class: LogoutSuccessHandlerImpl

Implements: LogoutSuccessHandler

### Fields:
- TokenService tokenService (Autowired)

### Methods:
- void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) (Override)

### Go Implementation Suggestion:
```go
package handle

type LogoutSuccessHandlerImpl struct {
	TokenService TokenService
}

func (c *LogoutSuccessHandlerImpl) onLogoutSuccess(request HttpServletRequest, response HttpServletResponse, authentication Authentication) {
	// TODO: Implement method
}

```

