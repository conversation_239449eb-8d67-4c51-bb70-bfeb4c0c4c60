# Package: com.ruoyi.framework.web.domain.server

## Class: Cpu

### Fields:
- int cpuNum
- double total
- double sys
- double used
- double wait
- double free

### Methods:
- int getCpuNum()
- void setCpuNum(int cpuNum)
- double getTotal()
- void setTotal(double total)
- double getSys()
- void setSys(double sys)
- double getUsed()
- void setUsed(double used)
- double getWait()
- void setWait(double wait)
- double getFree()
- void setFree(double free)

### Go Implementation Suggestion:
```go
package server

type Cpu struct {
	CpuNum int
	Total float64
	Sys float64
	Used float64
	Wait float64
	Free float64
}

func (c *Cpu) getCpuNum() int {
	// TODO: Implement method
	return 0
}

func (c *Cpu) setCpuNum(cpuNum int) {
	// TODO: Implement method
}

func (c *Cpu) getTotal() float64 {
	// TODO: Implement method
	return 0
}

func (c *Cpu) setTotal(total float64) {
	// TODO: Implement method
}

func (c *Cpu) getSys() float64 {
	// TODO: Implement method
	return 0
}

func (c *Cpu) setSys(sys float64) {
	// TODO: Implement method
}

func (c *Cpu) getUsed() float64 {
	// TODO: Implement method
	return 0
}

func (c *Cpu) setUsed(used float64) {
	// TODO: Implement method
}

func (c *Cpu) getWait() float64 {
	// TODO: Implement method
	return 0
}

func (c *Cpu) setWait(wait float64) {
	// TODO: Implement method
}

func (c *Cpu) getFree() float64 {
	// TODO: Implement method
	return 0
}

func (c *Cpu) setFree(free float64) {
	// TODO: Implement method
}

```

## Class: Jvm

### Fields:
- double total
- double max
- double free
- String version
- String home

### Methods:
- double getTotal()
- void setTotal(double total)
- double getMax()
- void setMax(double max)
- double getFree()
- void setFree(double free)
- double getUsed()
- double getUsage()
- String getName()
- String getVersion()
- void setVersion(String version)
- String getHome()
- void setHome(String home)
- String getStartTime()
- String getRunTime()
- String getInputArgs()

### Go Implementation Suggestion:
```go
package server

type Jvm struct {
	Total float64
	Max float64
	Free float64
	Version string
	Home string
}

func (c *Jvm) getTotal() float64 {
	// TODO: Implement method
	return 0
}

func (c *Jvm) setTotal(total float64) {
	// TODO: Implement method
}

func (c *Jvm) getMax() float64 {
	// TODO: Implement method
	return 0
}

func (c *Jvm) setMax(max float64) {
	// TODO: Implement method
}

func (c *Jvm) getFree() float64 {
	// TODO: Implement method
	return 0
}

func (c *Jvm) setFree(free float64) {
	// TODO: Implement method
}

func (c *Jvm) getUsed() float64 {
	// TODO: Implement method
	return 0
}

func (c *Jvm) getUsage() float64 {
	// TODO: Implement method
	return 0
}

func (c *Jvm) getName() string {
	// TODO: Implement method
	return ""
}

func (c *Jvm) getVersion() string {
	// TODO: Implement method
	return ""
}

func (c *Jvm) setVersion(version string) {
	// TODO: Implement method
}

func (c *Jvm) getHome() string {
	// TODO: Implement method
	return ""
}

func (c *Jvm) setHome(home string) {
	// TODO: Implement method
}

func (c *Jvm) getStartTime() string {
	// TODO: Implement method
	return ""
}

func (c *Jvm) getRunTime() string {
	// TODO: Implement method
	return ""
}

func (c *Jvm) getInputArgs() string {
	// TODO: Implement method
	return ""
}

```

## Class: Mem

### Fields:
- double total
- double used
- double free

### Methods:
- double getTotal()
- void setTotal(long total)
- double getUsed()
- void setUsed(long used)
- double getFree()
- void setFree(long free)
- double getUsage()

### Go Implementation Suggestion:
```go
package server

type Mem struct {
	Total float64
	Used float64
	Free float64
}

func (c *Mem) getTotal() float64 {
	// TODO: Implement method
	return 0
}

func (c *Mem) setTotal(total int64) {
	// TODO: Implement method
}

func (c *Mem) getUsed() float64 {
	// TODO: Implement method
	return 0
}

func (c *Mem) setUsed(used int64) {
	// TODO: Implement method
}

func (c *Mem) getFree() float64 {
	// TODO: Implement method
	return 0
}

func (c *Mem) setFree(free int64) {
	// TODO: Implement method
}

func (c *Mem) getUsage() float64 {
	// TODO: Implement method
	return 0
}

```

## Class: Sys

### Fields:
- String computerName
- String computerIp
- String userDir
- String osName
- String osArch

### Methods:
- String getComputerName()
- void setComputerName(String computerName)
- String getComputerIp()
- void setComputerIp(String computerIp)
- String getUserDir()
- void setUserDir(String userDir)
- String getOsName()
- void setOsName(String osName)
- String getOsArch()
- void setOsArch(String osArch)

### Go Implementation Suggestion:
```go
package server

type Sys struct {
	ComputerName string
	ComputerIp string
	UserDir string
	OsName string
	OsArch string
}

func (c *Sys) getComputerName() string {
	// TODO: Implement method
	return ""
}

func (c *Sys) setComputerName(computerName string) {
	// TODO: Implement method
}

func (c *Sys) getComputerIp() string {
	// TODO: Implement method
	return ""
}

func (c *Sys) setComputerIp(computerIp string) {
	// TODO: Implement method
}

func (c *Sys) getUserDir() string {
	// TODO: Implement method
	return ""
}

func (c *Sys) setUserDir(userDir string) {
	// TODO: Implement method
}

func (c *Sys) getOsName() string {
	// TODO: Implement method
	return ""
}

func (c *Sys) setOsName(osName string) {
	// TODO: Implement method
}

func (c *Sys) getOsArch() string {
	// TODO: Implement method
	return ""
}

func (c *Sys) setOsArch(osArch string) {
	// TODO: Implement method
}

```

## Class: SysFile

### Fields:
- String dirName
- String sysTypeName
- String typeName
- String total
- String free
- String used
- double usage

### Methods:
- String getDirName()
- void setDirName(String dirName)
- String getSysTypeName()
- void setSysTypeName(String sysTypeName)
- String getTypeName()
- void setTypeName(String typeName)
- String getTotal()
- void setTotal(String total)
- String getFree()
- void setFree(String free)
- String getUsed()
- void setUsed(String used)
- double getUsage()
- void setUsage(double usage)

### Go Implementation Suggestion:
```go
package server

type SysFile struct {
	DirName string
	SysTypeName string
	TypeName string
	Total string
	Free string
	Used string
	Usage float64
}

func (c *SysFile) getDirName() string {
	// TODO: Implement method
	return ""
}

func (c *SysFile) setDirName(dirName string) {
	// TODO: Implement method
}

func (c *SysFile) getSysTypeName() string {
	// TODO: Implement method
	return ""
}

func (c *SysFile) setSysTypeName(sysTypeName string) {
	// TODO: Implement method
}

func (c *SysFile) getTypeName() string {
	// TODO: Implement method
	return ""
}

func (c *SysFile) setTypeName(typeName string) {
	// TODO: Implement method
}

func (c *SysFile) getTotal() string {
	// TODO: Implement method
	return ""
}

func (c *SysFile) setTotal(total string) {
	// TODO: Implement method
}

func (c *SysFile) getFree() string {
	// TODO: Implement method
	return ""
}

func (c *SysFile) setFree(free string) {
	// TODO: Implement method
}

func (c *SysFile) getUsed() string {
	// TODO: Implement method
	return ""
}

func (c *SysFile) setUsed(used string) {
	// TODO: Implement method
}

func (c *SysFile) getUsage() float64 {
	// TODO: Implement method
	return 0
}

func (c *SysFile) setUsage(usage float64) {
	// TODO: Implement method
}

```

