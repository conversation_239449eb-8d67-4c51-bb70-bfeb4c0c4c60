# Package: com.ruoyi.framework.web.domain

## Class: Server

### Fields:
- int OSHI_WAIT_SECOND
- Cpu cpu
- Mem mem
- Jvm jvm
- Sys sys
- List<SysFile> sysFiles

### Methods:
- Cpu getCpu()
- void setCpu(Cpu cpu)
- Mem getMem()
- void setMem(Mem mem)
- Jvm getJvm()
- void setJvm(Jvm jvm)
- Sys getSys()
- void setSys(Sys sys)
- List<SysFile> getSysFiles()
- void setSysFiles(List<SysFile> sysFiles)
- void copyTo()
- void setCpuInfo(CentralProcessor processor)
- void setMemInfo(GlobalMemory memory)
- void setSysInfo()
- void setJvmInfo()
- void setSysFiles(OperatingSystem os)
- String convertFileSize(long size)

### Go Implementation Suggestion:
```go
package domain

type Server struct {
	OSHI_WAIT_SECOND int
	Cpu Cpu
	Mem Mem
	Jvm Jvm
	Sys Sys
	SysFiles []SysFile
}

func (c *Server) getCpu() Cpu {
	// TODO: Implement method
	return nil
}

func (c *Server) setCpu(cpu Cpu) {
	// TODO: Implement method
}

func (c *Server) getMem() Mem {
	// TODO: Implement method
	return nil
}

func (c *Server) setMem(mem Mem) {
	// TODO: Implement method
}

func (c *Server) getJvm() Jvm {
	// TODO: Implement method
	return nil
}

func (c *Server) setJvm(jvm Jvm) {
	// TODO: Implement method
}

func (c *Server) getSys() Sys {
	// TODO: Implement method
	return nil
}

func (c *Server) setSys(sys Sys) {
	// TODO: Implement method
}

func (c *Server) getSysFiles() []SysFile {
	// TODO: Implement method
	return nil
}

func (c *Server) setSysFiles(sysFiles []SysFile) {
	// TODO: Implement method
}

func (c *Server) copyTo() {
	// TODO: Implement method
}

func (c *Server) setCpuInfo(processor CentralProcessor) {
	// TODO: Implement method
}

func (c *Server) setMemInfo(memory GlobalMemory) {
	// TODO: Implement method
}

func (c *Server) setSysInfo() {
	// TODO: Implement method
}

func (c *Server) setJvmInfo() {
	// TODO: Implement method
}

func (c *Server) setSysFiles(os OperatingSystem) {
	// TODO: Implement method
}

func (c *Server) convertFileSize(size int64) string {
	// TODO: Implement method
	return ""
}

```

