# Package: com.ruoyi.framework.web.exception

## Class: GlobalExceptionHandler

### Fields:
- Logger log

### Methods:
- AjaxResult handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request) (ExceptionHandler)
- AjaxResult handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e, HttpServletRequest request) (ExceptionHandler)
- AjaxResult handleServiceException(ServiceException e, HttpServletRequest request) (ExceptionHandler)
- AjaxResult handleMissingPathVariableException(MissingPathVariableException e, HttpServletRequest request) (ExceptionHandler)
- AjaxResult handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) (ExceptionHandler)
- AjaxResult handleRuntimeException(RuntimeException e, HttpServletRequest request) (ExceptionHandler)
- AjaxResult handleException(Exception e, HttpServletRequest request) (ExceptionHandler)
- AjaxResult handleBindException(BindException e) (ExceptionHandler)
- Object handleMethodArgumentNotValidException(MethodArgumentNotValidException e) (ExceptionHandler)
- AjaxResult handleDemoModeException(DemoModeException e) (ExceptionHandler)

### Go Implementation Suggestion:
```go
package exception

type GlobalExceptionHandler struct {
	Log Logger
}

func (c *GlobalExceptionHandler) handleAccessDeniedException(e AccessDeniedException, request HttpServletRequest) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GlobalExceptionHandler) handleHttpRequestMethodNotSupported(e HttpRequestMethodNotSupportedException, request HttpServletRequest) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GlobalExceptionHandler) handleServiceException(e ServiceException, request HttpServletRequest) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GlobalExceptionHandler) handleMissingPathVariableException(e MissingPathVariableException, request HttpServletRequest) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GlobalExceptionHandler) handleMethodArgumentTypeMismatchException(e MethodArgumentTypeMismatchException, request HttpServletRequest) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GlobalExceptionHandler) handleRuntimeException(e RuntimeException, request HttpServletRequest) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GlobalExceptionHandler) handleException(e Exception, request HttpServletRequest) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GlobalExceptionHandler) handleBindException(e BindException) AjaxResult {
	// TODO: Implement method
	return nil
}

func (c *GlobalExceptionHandler) handleMethodArgumentNotValidException(e MethodArgumentNotValidException) interface{} {
	// TODO: Implement method
	return nil
}

func (c *GlobalExceptionHandler) handleDemoModeException(e DemoModeException) AjaxResult {
	// TODO: Implement method
	return nil
}

```

