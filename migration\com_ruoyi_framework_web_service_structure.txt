# Package: com.ruoyi.framework.web.service

## Class: PermissionService

### Fields:

### Methods:
- boolean hasPermi(String permission)
- boolean lacksPermi(String permission)
- boolean hasAnyPermi(String permissions)
- boolean hasRole(String role)
- boolean lacksRole(String role)
- boolean hasAnyRoles(String roles)
- boolean hasPermissions(Set<String> permissions, String permission)

### Go Implementation Suggestion:
```go
package service

type PermissionService struct {
}

func (c *PermissionService) hasPermi(permission string) bool {
	// TODO: Implement method
	return false
}

func (c *PermissionService) lacksPermi(permission string) bool {
	// TODO: Implement method
	return false
}

func (c *PermissionService) hasAnyPermi(permissions string) bool {
	// TODO: Implement method
	return false
}

func (c *PermissionService) hasRole(role string) bool {
	// TODO: Implement method
	return false
}

func (c *PermissionService) lacksRole(role string) bool {
	// TODO: Implement method
	return false
}

func (c *PermissionService) hasAnyRoles(roles string) bool {
	// TODO: Implement method
	return false
}

func (c *PermissionService) hasPermissions(permissions Set<String>, permission string) bool {
	// TODO: Implement method
	return false
}

```

## Class: SysLoginService

### Fields:
- TokenService tokenService (Autowired)
- AuthenticationManager authenticationManager (Resource)
- RedisCache redisCache (Autowired)
- ISysUserService userService (Autowired)
- ISysConfigService configService (Autowired)

### Methods:
- String login(String username, String password, String code, String uuid)
- void validateCaptcha(String username, String code, String uuid)
- void loginPreCheck(String username, String password)
- void recordLoginInfo(Long userId)

### Go Implementation Suggestion:
```go
package service

type SysLoginService struct {
	TokenService TokenService
	AuthenticationManager AuthenticationManager
	RedisCache RedisCache
	UserService ISysUserService
	ConfigService ISysConfigService
}

func (c *SysLoginService) login(username string, password string, code string, uuid string) string {
	// TODO: Implement method
	return ""
}

func (c *SysLoginService) validateCaptcha(username string, code string, uuid string) {
	// TODO: Implement method
}

func (c *SysLoginService) loginPreCheck(username string, password string) {
	// TODO: Implement method
}

func (c *SysLoginService) recordLoginInfo(userId int64) {
	// TODO: Implement method
}

```

## Class: SysPasswordService

### Fields:
- RedisCache redisCache (Autowired)
- int maxRetryCount (Value)
- int lockTime (Value)

### Methods:
- String getCacheKey(String username)
- void validate(SysUser user)
- boolean matches(SysUser user, String rawPassword)
- void clearLoginRecordCache(String loginName)

### Go Implementation Suggestion:
```go
package service

type SysPasswordService struct {
	RedisCache RedisCache
	MaxRetryCount int
	LockTime int
}

func (c *SysPasswordService) getCacheKey(username string) string {
	// TODO: Implement method
	return ""
}

func (c *SysPasswordService) validate(user SysUser) {
	// TODO: Implement method
}

func (c *SysPasswordService) matches(user SysUser, rawPassword string) bool {
	// TODO: Implement method
	return false
}

func (c *SysPasswordService) clearLoginRecordCache(loginName string) {
	// TODO: Implement method
}

```

## Class: SysPermissionService

### Fields:
- ISysRoleService roleService (Autowired)
- ISysMenuService menuService (Autowired)

### Methods:
- Set<String> getRolePermission(SysUser user)
- Set<String> getMenuPermission(SysUser user)

### Go Implementation Suggestion:
```go
package service

type SysPermissionService struct {
	RoleService ISysRoleService
	MenuService ISysMenuService
}

func (c *SysPermissionService) getRolePermission(user SysUser) Set<String> {
	// TODO: Implement method
	return nil
}

func (c *SysPermissionService) getMenuPermission(user SysUser) Set<String> {
	// TODO: Implement method
	return nil
}

```

## Class: SysRegisterService

### Fields:
- ISysUserService userService (Autowired)
- ISysConfigService configService (Autowired)
- RedisCache redisCache (Autowired)

### Methods:
- String register(RegisterBody registerBody)
- void validateCaptcha(String username, String code, String uuid)

### Go Implementation Suggestion:
```go
package service

type SysRegisterService struct {
	UserService ISysUserService
	ConfigService ISysConfigService
	RedisCache RedisCache
}

func (c *SysRegisterService) register(registerBody RegisterBody) string {
	// TODO: Implement method
	return ""
}

func (c *SysRegisterService) validateCaptcha(username string, code string, uuid string) {
	// TODO: Implement method
}

```

## Class: TokenService

### Fields:
- Logger log
- String header (Value)
- String secret (Value)
- int expireTime (Value)
- long MILLIS_SECOND
- long MILLIS_MINUTE
- Long MILLIS_MINUTE_TWENTY
- RedisCache redisCache (Autowired)

### Methods:
- LoginUser getLoginUser(HttpServletRequest request)
- void setLoginUser(LoginUser loginUser)
- void delLoginUser(String token)
- String createToken(LoginUser loginUser)
- void verifyToken(LoginUser loginUser)
- void refreshToken(LoginUser loginUser)
- void setUserAgent(LoginUser loginUser)
- String createToken(Map<String, Object> claims)
- Claims parseToken(String token)
- String getUsernameFromToken(String token)
- String getToken(HttpServletRequest request)
- String getTokenKey(String uuid)

### Go Implementation Suggestion:
```go
package service

type TokenService struct {
	Log Logger
	Header string
	Secret string
	ExpireTime int
	MILLIS_SECOND int64
	MILLIS_MINUTE int64
	MILLIS_MINUTE_TWENTY int64
	RedisCache RedisCache
}

func (c *TokenService) getLoginUser(request HttpServletRequest) LoginUser {
	// TODO: Implement method
	return nil
}

func (c *TokenService) setLoginUser(loginUser LoginUser) {
	// TODO: Implement method
}

func (c *TokenService) delLoginUser(token string) {
	// TODO: Implement method
}

func (c *TokenService) createToken(loginUser LoginUser) string {
	// TODO: Implement method
	return ""
}

func (c *TokenService) verifyToken(loginUser LoginUser) {
	// TODO: Implement method
}

func (c *TokenService) refreshToken(loginUser LoginUser) {
	// TODO: Implement method
}

func (c *TokenService) setUserAgent(loginUser LoginUser) {
	// TODO: Implement method
}

func (c *TokenService) createToken(Object> map[string]interface{}) string {
	// TODO: Implement method
	return ""
}

func (c *TokenService) parseToken(token string) Claims {
	// TODO: Implement method
	return nil
}

func (c *TokenService) getUsernameFromToken(token string) string {
	// TODO: Implement method
	return ""
}

func (c *TokenService) getToken(request HttpServletRequest) string {
	// TODO: Implement method
	return ""
}

func (c *TokenService) getTokenKey(uuid string) string {
	// TODO: Implement method
	return ""
}

```

## Class: UserDetailsServiceImpl

Implements: UserDetailsService

### Fields:
- Logger log
- ISysUserService userService (Autowired)
- SysPasswordService passwordService (Autowired)
- SysPermissionService permissionService (Autowired)

### Methods:
- UserDetails loadUserByUsername(String username) (Override)
- UserDetails createLoginUser(SysUser user)

### Go Implementation Suggestion:
```go
package service

type UserDetailsServiceImpl struct {
	Log Logger
	UserService ISysUserService
	PasswordService SysPasswordService
	PermissionService SysPermissionService
}

func (c *UserDetailsServiceImpl) loadUserByUsername(username string) UserDetails {
	// TODO: Implement method
	return nil
}

func (c *UserDetailsServiceImpl) createLoginUser(user SysUser) UserDetails {
	// TODO: Implement method
	return nil
}

```

